# VIP专属二维码功能演示说明

## 🎯 功能入口
**位置**: 团队管理页面 → 搜索栏右侧
**触发条件**: 仅区域总监（userLevelId == 2）可见
**按钮样式**: 金色渐变背景，"二维码"文字

```
[搜索框] [🔍] [二维码] ← 金色按钮，仅VIP可见
```

## 🏆 VIP专属弹窗展示

### 顶部区域 - 皇冠标识
```
┌─────────────────────────────────────┐
│ 👑    VIP专属                    ✕ │
│      推广二维码                      │
└─────────────────────────────────────┘
```
- 皇冠图标带跳跃动画
- 金色渐变背景
- 光影流转效果

### 用户信息区域
```
┌─────────────────────────────────────┐
│  [头像]     张三                     │
│   VIP      区域总监                  │
└─────────────────────────────────────┘
```
- 头像带金色边框
- VIP标识突出显示
- 用户昵称和等级

### 二维码展示区域
```
┌─────────────────────────────────────┐
│  ┌─────────────────────────────────┐ │
│  │                                 │ │
│  │        [二维码图片]              │ │
│  │                                 │ │
│  │                    伍俊惠选 ←logo│ │
│  └─────────────────────────────────┘ │
│                                     │
│  专属推广码: promo_12345  [📋]       │
└─────────────────────────────────────┘
```
- 二维码带旋转金色边框
- 右下角品牌logo
- 推广码可复制

### VIP特权说明
```
┌─────────────────────────────────────┐
│ 🎯 专属推广链接，永久有效             │
│ 💎 VIP身份标识，彰显尊贵             │
│ 🚀 推广收益实时到账                 │
└─────────────────────────────────────┘
```

### 操作按钮区域
```
┌─────────────────────────────────────┐
│  [💾 保存到相册]  [📤 立即分享]      │
└─────────────────────────────────────┘
```
- 左侧金色渐变按钮
- 右侧白色边框按钮
- 图标+文字组合

## 🎪 动画效果展示

### 1. 加载状态
```
    ⟲ 正在生成专属二维码...
    ● ● ●  ← 跳跃式加载点
```

### 2. 交互反馈
- 按钮点击：缩放效果 (scale: 0.96)
- 皇冠图标：上下跳跃动画
- 边框：360度旋转动画
- 背景：光影流转效果

## 📱 响应式适配

### 大屏幕 (>750rpx)
- 弹窗宽度: 650rpx
- 二维码尺寸: 320rpx × 320rpx
- 横向布局用户信息

### 小屏幕 (≤750rpx)
- 弹窗宽度: 95%
- 二维码尺寸: 280rpx × 280rpx
- 纵向布局用户信息
- 按钮垂直排列

## 🔧 功能操作流程

### 1. 生成二维码
```
点击"二维码"按钮 → 显示弹窗 → 调用API → 显示二维码
```

### 2. 复制推广码
```
点击复制按钮 → 复制到剪贴板 → 显示成功提示
```

### 3. 保存到相册
```
点击保存按钮 → 处理图片格式 → 保存到相册 → 显示结果
```

### 4. 分享功能
```
点击分享按钮 → 调用分享菜单 → 提示用户操作
```

## 🎨 视觉设计特点

### 色彩方案
- **主色**: 金色系 (#FFD700, #FFA500, #FF8C00)
- **辅色**: 深棕色 (#8B4513)
- **背景**: 白色渐变 (#ffffff → #f8f9fa)

### 字体层级
- **标题**: 36rpx, 加粗, 金色
- **副标题**: 24rpx, 中等, 橙色
- **正文**: 26-28rpx, 常规, 深灰
- **推广码**: 28rpx, 加粗, 等宽字体

### 间距规范
- **外边距**: 20-40rpx
- **内边距**: 15-30rpx
- **组件间距**: 20-25rpx
- **按钮高度**: 80rpx

## 💡 用户体验亮点

### 1. 视觉冲击
- 金色主题彰显尊贵
- 动画效果增强吸引力
- 层次分明的信息架构

### 2. 操作便捷
- 一键生成二维码
- 快速复制推广码
- 直接保存到相册

### 3. 身份认同
- VIP专属标识
- 个性化用户信息
- 专属特权说明

### 4. 品牌露出
- 二维码内嵌品牌logo
- 统一的视觉风格
- 专业的设计质感

## 🚀 技术实现要点

### 1. 条件渲染
```javascript
wx:if="{{userLevelId == 2}}"  // 仅区域总监可见
```

### 2. 动态数据绑定
```javascript
src="{{qrCodeData.qrCodeUrl}}"     // 二维码图片
{{qrCodeData.scene}}               // 推广码
{{userInfo.nickname}}              // 用户昵称
```

### 3. 事件处理
```javascript
bindtap="showQrCodeModal"          // 显示弹窗
bindtap="copyPromotionCode"        // 复制推广码
bindtap="saveQrCodeToAlbum"        // 保存图片
```

### 4. 样式动画
```css
animation: bounce 2s ease-in-out infinite;     // 皇冠跳跃
animation: rotate-border 3s linear infinite;   // 边框旋转
animation: shimmer 3s ease-in-out infinite;    // 光影效果
```

## 📊 预期效果

### 1. 用户满意度
- 提升VIP用户的尊贵感受
- 增强品牌认同和忠诚度
- 改善整体用户体验

### 2. 推广效果
- 专属标识提升转化率
- 便捷操作增加分享频次
- 品牌露出扩大影响力

### 3. 商业价值
- 强化会员等级差异化
- 提升用户留存率
- 增加推广收益

这个VIP专属二维码功能通过精心的视觉设计和交互体验，充分体现了区域总监的尊贵身份，同时提供了便捷的推广工具，实现了功能性和体验性的完美结合。
