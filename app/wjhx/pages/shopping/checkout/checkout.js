var util = require('../../../utils/util.js');
var api = require('../../../config/api.js');
const pay = require('../../../services/pay.js');

var app = getApp();

Page({
  data: {
    checkedGoodsList: [],
    checkedAddress: {},
    checkedCoupon: null,
    couponList: [],
    availableCoupons: [],
    goodsTotalPrice: 0.00, //商品总价
    freightPrice: 0.00,    //快递费
    couponPrice: 0.00,     //优惠券的价格
    orderTotalPrice: 0.00,  //订单总价
    actualPrice: 0.00,     //实际需要支付的总价
    addressId: null,
    couponId: 0,
    type: 0,
    goodsId: 0,            // 商品ID
    productId: 0,          // 产品ID（规格商品）
    number: 1,             // 商品数量
    currentPrice: 0,       // 当前选中规格的价格
    couponName: '',        // 优惠券名称
    isVoucherOrder: false, // 是否为礼券兑换订单

    // 用户余额相关
    userBalance: 0.00,     // 用户余额
    useBalance: 0.00,      // 使用的余额
    balancePrice: 0.00,    // 余额抵扣金额
    useBalanceEnabled: false, // 是否启用余额抵扣
    maxUsableBalance: 0.00, // 最大可用余额

    // 积分相关
    userPoints: 0,         // 用户积分
    usePoints: 0,          // 使用的积分
    pointsPrice: 0.00,     // 积分抵扣金额
    usePointsEnabled: false, // 是否启用积分抵扣
    maxUsablePoints: 0,    // 最大可用积分
    pointsConfig: {        // 积分配置
      useRate: 100,
      earnRate: 1,
      minUsePoints: 100
    },

    // 运费提醒相关
    showFreightNotice: false,  // 是否显示运费提醒
    freightNoticeText: '',     // 运费提醒文本
    
    // 推广相关
    promoterId: null           // 推广者ID
  },
  onLoad: function (options) {
    if (Object.keys(options).length != 0) {
      wx.setStorageSync('orderOptions', options)
    } else {
      options = wx.getStorageSync('orderOptions')
    }
    try {
      var addressId = wx.getStorageSync('addressId');
      if (addressId && addressId !== 'null' && addressId > 0) {
        this.setData({ 'addressId': addressId });
        console.log('Loaded addressId from storage:', addressId);
      } else {
        this.setData({ 'addressId': null });
        console.log('No valid addressId in storage, setting to null');
      }

      // 检查是否是从优惠券选择页面返回（有skipCheckoutReload标志）
      const skipReload = wx.getStorageSync('skipCheckoutReload');
      let storageCouponId = 0;

      if (skipReload) {
        // 如果是从优惠券选择页面返回，可以使用storage中的优惠券ID
        storageCouponId = wx.getStorageSync('couponId') || 0;
        console.log('从优惠券选择页面返回，加载storage中的优惠券ID:', storageCouponId);
      } else {
        // 新订单默认不选择优惠券，让用户主动选择
        console.log('新订单初始化，优惠券ID设置为0，用户需要主动选择');
      }

      this.setData({ 'couponId': parseInt(storageCouponId) || 0 });

      // 获取传递过来的优惠券ID、优惠券名称、商品ID、产品ID、数量、价格和推广者ID
      if (options) {
        let couponId = options.couponId || 0;
        let goodsId = options.goodsId || 0;
        let productId = options.productId || 0;
        let number = parseInt(options.number) || 1;
        let currentPrice = parseFloat(options.currentPrice) || 0;
        let couponName = options.couponName || '';
        let type = options.type || 0;
        let promoterId = options.promoterId ? parseInt(options.promoterId) : null;

        console.log('页面参数:', {
          couponId, goodsId, productId, number, currentPrice, couponName, type, promoterId
        });

        // 检查是否为礼券兑换订单
        // 礼券订单的判断条件：
        // 1. 必须有goodsId（单个商品）
        // 2. type必须明确指示为礼券订单（type == 0 表示礼券兑换，type == 1 表示付费商品）
        // 3. 普通购物车结算不会有goodsId，只有购物车数据
        let isVoucherOrder = false;

        // 如果没有goodsId，肯定是普通购物车结算
        if (goodsId <= 0) {
          isVoucherOrder = false;
        } else {
          // 有goodsId的情况下，根据type判断
          // type == 0: 礼券兑换订单（免费）
          // type == 1: 付费商品订单
          // 其他情况或未指定type: 普通购物车结算
          if (type == 0) {
            isVoucherOrder = true; // 礼券兑换订单
          } else {
            isVoucherOrder = false; // 付费商品订单或普通结算，都需要价格计算
          }
        }

        // 优先使用已经从storage加载的couponId（如果是从优惠券选择页面返回）
        // 其次使用URL参数中的couponId（如果有的话）
        // 最后默认为0（新订单）
        const finalCouponId = this.data.couponId > 0 ? this.data.couponId : (parseInt(couponId) || 0);

        this.setData({
          'couponId': finalCouponId,
          'goodsId': parseInt(goodsId),
          'productId': parseInt(productId),
          'number': number,
          'currentPrice': currentPrice,
          'couponName': couponName,
          'isVoucherOrder': isVoucherOrder,
          'type': type,
          'promoterId': promoterId
        });

        console.log('优惠券ID选择逻辑:', {
          'URL参数中的couponId': couponId,
          '最终使用的couponId': finalCouponId,
          '说明': finalCouponId > 0 ? '使用URL参数中的优惠券ID' : '新订单，未选择优惠券'
        });

        console.log('Order info:', {
          isVoucherOrder: isVoucherOrder,
          goodsId: goodsId,
          productId: productId,
          couponId: couponId,
          type: type
        });

        console.log('订单类型判断:', {
          'type == 0': type == 0,
          'goodsId > 0': goodsId > 0,
          'couponId > 0': couponId > 0,
          'isVoucherOrder': isVoucherOrder
        });
      }
    } catch (e) {
      console.error('Error loading storage data:', e);
    }
  },

  onShow: function () {
    // 页面显示时获取用户余额和积分信息
    this.getUserBalance();
    this.getUserPoints();
  },

  // 获取用户余额
  getUserBalance: function () {
    const that = this;
    util.request(api.UserBalance, {}, 'GET').then(function (res) {
      if (res.errno === 0) {
        console.log('获取用户余额成功:', res.data);
        that.setData({
          userBalance: res.data.balance || 0
        });
        // 重新计算最大可用余额
        that.updateMaxUsableBalance();
      } else {
        console.error('获取用户余额失败:', res.errmsg);
        // 设置默认值
        that.setData({
          userBalance: 0
        });
      }
    }).catch(function (err) {
      console.error('获取用户余额异常:', err);
      that.setData({
        userBalance: 0
      });
    });
  },

  // 获取用户积分信息
  getUserPoints: function () {
    const that = this;
    util.request(api.UserPoints, {}, 'GET').then(function (res) {
      if (res.errno === 0) {
        console.log('获取用户积分成功:', res.data);
        that.setData({
          userPoints: res.data.points || 0,
          pointsConfig: {
            useRate: res.data.useRate || 100,
            earnRate: res.data.earnRate || 1,
            minUsePoints: res.data.minUsePoints || 100,
            maxUseRatio: res.data.maxUseRatio || 100
          }
        });
        // 重新计算最大可用积分
        that.updateMaxUsablePoints();
      } else {
        console.error('获取用户积分失败:', res.errmsg);
      }
    }).catch(function (err) {
      console.error('获取用户积分异常:', err);
    });
  },

  // 更新最大可用余额
  updateMaxUsableBalance: function () {
    const maxUsableBalance = this.calculateMaxUsableBalance();
    this.setData({
      maxUsableBalance: maxUsableBalance
    });
    console.log('更新最大可用余额:', maxUsableBalance);
  },

  // 更新最大可用积分
  updateMaxUsablePoints: function () {
    if (this.data.pointsConfig && this.data.userPoints > 0) {
      const goodsTotalPrice = this.data.goodsTotalPrice || 0;
      const freightPrice = this.data.freightPrice || 0;
      const baseOrderAmount = goodsTotalPrice + freightPrice;

      // 计算基础的最大可用积分（不考虑其他抵扣方式的影响）
      const maxUseRatio = this.data.pointsConfig.maxUseRatio || 100;
      const maxDeductAmount = baseOrderAmount * (maxUseRatio / 100);
      const maxPointsByAmount = Math.floor(maxDeductAmount * this.data.pointsConfig.useRate);
      const baseMaxUsablePoints = Math.min(this.data.userPoints, maxPointsByAmount);

      // 检查最小使用限制
      const finalMaxUsablePoints = baseMaxUsablePoints >= this.data.pointsConfig.minUsePoints ? baseMaxUsablePoints : 0;

      this.setData({
        maxUsablePoints: finalMaxUsablePoints
      });

      console.log('更新最大可用积分:', {
        userPoints: this.data.userPoints,
        baseOrderAmount: baseOrderAmount,
        maxUseRatio: maxUseRatio,
        maxDeductAmount: maxDeductAmount,
        maxPointsByAmount: maxPointsByAmount,
        finalMaxUsablePoints: finalMaxUsablePoints,
        minUsePoints: this.data.pointsConfig.minUsePoints
      });
    }
  },

  getCheckoutInfo: function () {
    let that = this;
    wx.showLoading({
      title: '加载中...',
      mask: true
    });
    // 处理礼券兑换订单
    console.log('getCheckoutInfo订单类型检查:', {
      isVoucherOrder: that.data.isVoucherOrder,
      type: that.data.type,
      goodsId: that.data.goodsId,
      '是否单商品订单': that.data.goodsId > 0,
      '是否使用单商品流程': that.data.goodsId > 0 && (that.data.type == 0 || that.data.type == 1),
      '判断条件': {
        'goodsId > 0': that.data.goodsId > 0,
        'type == 0': that.data.type == 0,
        'type == 1': that.data.type == 1
      }
    });

    // 只有明确的单商品订单才使用单商品处理流程
    // 普通购物车结算即使有goodsId参数也应该使用购物车流程
    if (that.data.goodsId > 0 && (that.data.type == 0 || that.data.type == 1)) {
      // 先获取商品信息
      util.request(api.GoodsDetail, { id: that.data.goodsId })
        .then(function (goodsRes) {
          if (goodsRes.success) {
            const goods = goodsRes.data.goods;

            // 获取地址信息
            util.request(api.AddressList)
              .then(function (addressRes) {
                wx.hideLoading();

                // 默认选中第一个地址
                let checkedAddress = null;
                if (addressRes.success && addressRes.data.length > 0) {
                  if (that.data.addressId && that.data.addressId !== 'null' && that.data.addressId > 0) {
                    // 如果有选择的地址ID，则查找对应地址
                    checkedAddress = addressRes.data.find(item => item.id === that.data.addressId) || addressRes.data[0];
                  } else {
                    // 否则选择默认地址或第一个地址
                    checkedAddress = addressRes.data.find(item => item.isDefault) || addressRes.data[0];
                    that.setData({
                      addressId: checkedAddress.id
                    });
                  }
                }

                // 使用传递的价格或商品默认价格
                const actualPrice = that.data.currentPrice > 0 ? that.data.currentPrice : goods.retailPrice;

                // 构建商品对象
                const goodsItem = {
                  id: goods.id,
                  goodsId: goods.id,
                  goodsSn: goods.goodsSn || '',
                  goodsName: goods.name,
                  type: that.type,
                  listPicUrl: goods.listPicUrl,
                  retailPrice: actualPrice, // 使用实际选中的价格
                  marketPrice: goods.marketPrice || actualPrice,
                  number: that.data.number, // 使用传递的数量
                  goodsSpecificationIds: '',
                  goodsSpecificationValues: [],
                  checked: true
                };

                // 计算价格 - 根据数量和实际价格计算总价
                const goodsTotalPrice = actualPrice * that.data.number;
                const freightPrice = 0.00; // 运费
                let couponPrice = 0.00;
                let orderTotalPrice = goodsTotalPrice + freightPrice;
                let finalActualPrice = orderTotalPrice;

                if (that.data.type == 0) {
                  // 礼券兑换订单：优惠券抵扣全部金额，实付为0
                  couponPrice = goodsTotalPrice;
                  finalActualPrice = 0.00;
                  orderTotalPrice = 0.00;
                } else {
                  // 付费商品订单：需要支付，可以使用优惠券抵扣
                  // 如果选择了优惠券，需要调用后端API计算抵扣金额
                  if (that.data.couponId > 0) {
                    console.log('付费商品订单选择了优惠券，将在数据设置完成后重新计算价格');
                  }
                }

                // 先构建基本的优惠券对象
                let checkedCoupon = null;
                if (that.data.couponId > 0) {
                  // 创建基本的优惠券对象，稍后会从API数据中更新
                  checkedCoupon = {
                    id: that.data.couponId,
                    name: that.data.couponName || '优惠券',
                    amount: 0, // 默认金额，后续会从API数据中更新
                    minAmount: 0
                  };
                  console.log('创建基本优惠券对象:', checkedCoupon);
                }

                // 单商品订单也需要获取优惠券列表
                // 调用优惠券API获取可用优惠券
                util.request(api.CouponList, {
                  status: 'available',
                  page: 1,
                  size: 100
                }).then(function (couponRes) {
                  let availableCoupons = [];
                  if (couponRes.success && couponRes.data) {
                    // 筛选满足条件的优惠券
                    availableCoupons = couponRes.data.filter(coupon => {
                      return coupon.minAmount <= goodsTotalPrice;
                    });
                  }

                  console.log('单商品订单优惠券数据:', {
                    '全部优惠券': couponRes.data,
                    '可用优惠券': availableCoupons,
                    '商品总价': goodsTotalPrice
                  });

                  // 如果选择了优惠券，尝试从API数据中找到完整信息
                  if (that.data.couponId > 0 && couponRes.success && couponRes.data) {
                    const selectedCoupon = couponRes.data.find(coupon => coupon.id === that.data.couponId);
                    if (selectedCoupon) {
                      checkedCoupon = selectedCoupon;
                      console.log('从API数据中找到选中的优惠券:', selectedCoupon);
                    }
                  }

                  // 获取用户余额和积分信息
                  Promise.all([
                    util.request(api.UserBalance),
                    util.request(api.UserPoints)
                  ]).then(function ([balanceRes, pointsRes]) {
                    let userBalance = 0;
                    if (balanceRes.success && balanceRes.data) {
                      userBalance = balanceRes.data.balance || 0;
                    }
                    let userPoints = 0;
                    let pointsConfig = {
                      useRate: 100,
                      earnRate: 1,
                      minUsePoints: 100
                    };
                    let maxUsablePoints = 0;

                    if (pointsRes.success && pointsRes.data) {
                      userPoints = pointsRes.data.points || 0;
                      if (pointsRes.data.pointsConfig) {
                        pointsConfig = pointsRes.data.pointsConfig;
                      }
                      // 计算最大可用积分（基于配置的最大使用比例）
                      const maxUseRatio = pointsConfig.maxUseRatio || 100; // 默认100%
                      const maxDeductAmount = finalActualPrice * (maxUseRatio / 100);
                      const maxPointsByAmount = Math.floor(maxDeductAmount * pointsConfig.useRate);
                      maxUsablePoints = Math.min(userPoints, maxPointsByAmount);
                      if (userPoints < pointsConfig.minUsePoints) {
                        maxUsablePoints = 0;
                      }
                    }

                    // 计算最大可用余额
                    const maxUsableBalance = Math.min(userBalance, Math.max(0, finalActualPrice));

                    console.log('单商品订单用户信息:', {
                      userBalance,
                      userPoints,
                      pointsConfig,
                      maxUsablePoints,
                      maxUsableBalance,
                      orderAmount: finalActualPrice
                    });

                    // 更新页面数据
                    that.setData({
                      checkedGoodsList: [goodsItem],
                      checkedAddress: checkedAddress,
                      actualPrice: finalActualPrice,
                      checkedCoupon: checkedCoupon,
                      couponList: couponRes.data || [],
                      availableCoupons: availableCoupons,
                      couponPrice: couponPrice,
                      freightPrice: freightPrice,
                      goodsTotalPrice: goodsTotalPrice,
                      orderTotalPrice: orderTotalPrice,
                      userBalance: userBalance,
                      maxUsableBalance: maxUsableBalance,
                      useBalance: 0,
                      balancePrice: 0,
                      userPoints: userPoints,
                      pointsConfig: pointsConfig,
                      maxUsablePoints: maxUsablePoints,
                      usePoints: 0,
                      pointsPrice: 0,
                      usePointsEnabled: false,
                      useBalanceEnabled: false
                    });

                    // 更新运费提醒
                    that.updateFreightNotice();
                  }).catch(function (error) {
                    console.error('获取用户信息失败:', error);
                    // 即使获取用户信息失败，也要更新基本数据
                    that.setData({
                      checkedGoodsList: [goodsItem],
                      checkedAddress: checkedAddress,
                      actualPrice: finalActualPrice,
                      checkedCoupon: checkedCoupon,
                      couponList: couponRes.data || [],
                      availableCoupons: availableCoupons,
                      couponPrice: couponPrice,
                      freightPrice: freightPrice,
                      goodsTotalPrice: goodsTotalPrice,
                      orderTotalPrice: orderTotalPrice,
                      userBalance: 0,
                      maxUsableBalance: 0,
                      userPoints: 0,
                      maxUsablePoints: 0,
                      usePoints: 0,
                      pointsPrice: 0,
                      usePointsEnabled: false
                    });

                    // 更新运费提醒
                    that.updateFreightNotice();
                  });

                  // 如果是付费商品订单且选择了优惠券，需要重新计算价格
                  if (that.data.type == 1 && that.data.couponId > 0 && checkedCoupon) {
                    console.log('单商品付费订单选择了优惠券，开始重新计算价格');
                    setTimeout(() => {
                      that.calculateSingleGoodsPrice();
                    }, 100);
                  }
                }).catch(function (couponError) {
                  console.error('获取优惠券列表失败:', couponError);

                  // 即使获取优惠券失败，也要获取积分信息
                  util.request(api.UserPoints).then(function (pointsRes) {
                    let userPoints = 0;
                    let pointsConfig = {
                      useRate: 100,
                      earnRate: 1,
                      minUsePoints: 100
                    };
                    let maxUsablePoints = 0;

                    if (pointsRes.success && pointsRes.data) {
                      userPoints = pointsRes.data.points || 0;
                      if (pointsRes.data.pointsConfig) {
                        pointsConfig = pointsRes.data.pointsConfig;
                      }
                      // 计算最大可用积分（基于配置的最大使用比例）
                      const maxUseRatio = pointsConfig.maxUseRatio || 100; // 默认100%
                      const maxDeductAmount = finalActualPrice * (maxUseRatio / 100);
                      const maxPointsByAmount = Math.floor(maxDeductAmount * pointsConfig.useRate);
                      maxUsablePoints = Math.min(userPoints, maxPointsByAmount);
                      if (userPoints < pointsConfig.minUsePoints) {
                        maxUsablePoints = 0;
                      }
                    }

                    // 更新基本数据（包含积分信息）
                    that.setData({
                      checkedGoodsList: [goodsItem],
                      checkedAddress: checkedAddress,
                      actualPrice: finalActualPrice,
                      checkedCoupon: checkedCoupon,
                      couponList: [],
                      availableCoupons: [],
                      couponPrice: couponPrice,
                      freightPrice: freightPrice,
                      goodsTotalPrice: goodsTotalPrice,
                      orderTotalPrice: orderTotalPrice,
                      userPoints: userPoints,
                      pointsConfig: pointsConfig,
                      maxUsablePoints: maxUsablePoints,
                      usePoints: 0,
                      pointsPrice: 0,
                      usePointsEnabled: false
                    });

                    // 更新运费提醒
                    that.updateFreightNotice();
                  }).catch(function (pointsError) {
                    console.error('获取积分信息失败:', pointsError);
                    // 最终兜底，更新基本数据
                    that.setData({
                      checkedGoodsList: [goodsItem],
                      checkedAddress: checkedAddress,
                      actualPrice: finalActualPrice,
                      checkedCoupon: checkedCoupon,
                      couponList: [],
                      availableCoupons: [],
                      couponPrice: couponPrice,
                      freightPrice: freightPrice,
                      goodsTotalPrice: goodsTotalPrice,
                      orderTotalPrice: orderTotalPrice,
                      userPoints: 0,
                      maxUsablePoints: 0,
                      usePoints: 0,
                      pointsPrice: 0,
                      usePointsEnabled: false
                    });

                    // 更新运费提醒
                    that.updateFreightNotice();
                  });

                  // 如果是付费商品订单且选择了优惠券，需要重新计算价格
                  if (that.data.type == 1 && that.data.couponId > 0 && checkedCoupon) {
                    console.log('单商品付费订单选择了优惠券，开始重新计算价格（优惠券获取失败情况）');
                    setTimeout(() => {
                      that.calculateSingleGoodsPrice();
                    }, 100);
                  }
                });
              })
              .catch(function (error) {
                wx.hideLoading();
                util.showErrorToast('获取地址信息失败');
                console.error('Failed to get address:', error);
              });
          } else {
            wx.hideLoading();
            util.showErrorToast('获取商品信息失败');
          }
        })
        .catch(function (error) {
          wx.hideLoading();
          util.showErrorToast('获取商品信息失败');
          console.error('Failed to get goods:', error);
        });
    } else {
      // 常规购物车结算流程
      // Fix: Create a request object that properly handles null/undefined values
      const requestData = {
        couponId: that.data.couponId || 0,
        usePoints: 0,
        useBalance: 0
      };

      // Only include addressId if it has a valid value
      if (that.data.addressId && that.data.addressId !== 'null') {
        requestData.addressId = that.data.addressId;
      }

      console.log('Checkout request data:', requestData);

      util.request(api.CartAdvancedCheckout, requestData, 'POST').then(function (res) {
        if (res.success) {
          console.log(res.data);
          if (res.data.checkedAddress && res.data.checkedAddress.id) {
            that.setData({ addressId: res.data.checkedAddress.id })
          }
          // 构建更新数据，但保留手动设置的优惠券选择和已获取的用户信息
          const updateData = {
            checkedGoodsList: res.data.checkedGoodsList,
            checkedAddress: res.data.checkedAddress,
            actualPrice: res.data.actualPrice,
            couponList: res.data.couponList,
            availableCoupons: res.data.availableCoupons || res.data.couponList,
            couponPrice: res.data.couponPrice,
            freightPrice: res.data.freightPrice,
            goodsTotalPrice: res.data.goodsTotalPrice,
            orderTotalPrice: res.data.orderTotalPrice,
            // 优先使用已获取的用户余额，如果没有则使用API返回的
            userBalance: that.data.userBalance > 0 ? that.data.userBalance : (res.data.userBalance || 0),
            balancePrice: res.data.balancePrice || 0,
            // 优先使用已获取的用户积分，如果没有则使用API返回的
            userPoints: that.data.userPoints > 0 ? that.data.userPoints : (res.data.userPoints || 0),
            usePoints: res.data.usePoints || 0,
            pointsPrice: res.data.pointsPrice || 0,
            maxUsablePoints: res.data.maxUsablePoints || 0,
            // 根据后端返回的积分使用情况设置开关状态
            usePointsEnabled: (res.data.usePoints || 0) > 0,
            useBalanceEnabled: (res.data.balancePrice || 0) > 0
          };

          // 计算最大可用余额
          updateData.maxUsableBalance = that.calculateMaxUsableBalanceWithData(updateData);

          // 如果后端返回了积分配置，更新积分配置
          if (res.data.pointsConfig) {
            updateData.pointsConfig = res.data.pointsConfig;
          }

          // 只有在没有手动选择优惠券时，才使用后端返回的优惠券信息
          if (!that.data.checkedCoupon || !that.data.checkedCoupon.id) {
            updateData.checkedCoupon = res.data.checkedCoupon;
          }

          that.setData(updateData);

          // 更新运费提醒
          that.updateFreightNotice();

          console.log('getCheckoutInfo更新数据:', updateData);
          console.log('优惠券数据详情:', {
            'res.data.couponList': res.data.couponList,
            'res.data.availableCoupons': res.data.availableCoupons,
            '最终availableCoupons': updateData.availableCoupons,
            '数组长度': updateData.availableCoupons ? updateData.availableCoupons.length : 'undefined'
          });
        }
        wx.hideLoading();
      });
    }
  },
  selectAddress() {
    // 设置来自结算页的标志
    wx.setStorageSync('fromCheckout', true);

    // 保存当前选项参数
    const options = wx.getStorageSync('orderOptions') || {};
    if (Object.keys(options).length > 0) {
      wx.setStorageSync('orderParams', options);
    }

    // 跳转到地址选择页面
    wx.navigateTo({
      url: '/pages/shopping/address/address'
    });
  },
  addAddress() {
    // Set a flag to prevent flashing issues 
    wx.setStorageSync('fromCheckout', true);

    // Add loading to smooth transition 
    wx.showLoading({
      title: '加载中...',
    });

    // 构建URL，将所有结算相关的参数传递到地址添加页面
    let url = '/pages/shopping/addressAdd/addressAdd?source=checkout';


    console.log('前往地址添加页面，URL:', url);

    wx.navigateTo({
      url: url,
      success: () => {
        // Hide loading after navigation is successful
        setTimeout(() => {
          wx.hideLoading();
        }, 100);
      }
    });
  },
  onReady: function () {
    // 页面渲染完成

  },
  onShow: function () {
    // 页面显示
    console.log('checkout页面onShow，当前数据:', {
      couponId: this.data.couponId,
      checkedCoupon: this.data.checkedCoupon
    });

    // 检查是否有跳过重新加载的标志
    const skipReload = wx.getStorageSync('skipCheckoutReload');
    if (skipReload) {
      console.log('跳过重新加载checkout数据，但会重新计算价格');
      wx.removeStorageSync('skipCheckoutReload');

      // 虽然跳过重新加载，但仍需要重新计算价格以确保优惠券生效
      if (this.data.couponId > 0) {
        console.log('检测到优惠券选择，重新计算价格');
        this.recalculatePrice();
      }

      // 更新运费提醒（地址可能已更改）
      this.updateFreightNotice();
      return;
    }

    wx.showLoading({
      title: '加载中...',
    })
    this.getCheckoutInfo();

  },
  onHide: function () {
    // 页面隐藏

  },
  onUnload: function () {
    // 页面关闭

  },
  submitOrder: function () {

    const that = this;

    if (!this.data.addressId || this.data.addressId === 'null' || this.data.addressId <= 0) {
      util.showErrorToast('请选择收货地址');
      return false;
    }

    // 在提交订单前，先重新计算价格确保数据最新
    console.log('提交订单前重新计算价格，确保数据最新');

    wx.showLoading({
      title: '计算价格中...',
      mask: true
    });

    // 先重新计算价格，然后再提交订单
    this.recalculatePriceBeforeSubmit().then(() => {
      // 价格计算完成后，开始提交订单
      wx.showLoading({
        title: '提交订单中...',
        mask: true
      });

      that.doSubmitOrder();
    }).catch((error) => {
      wx.hideLoading();
      console.error('价格计算失败:', error);
      util.showErrorToast('价格计算失败，请重试');
    });
  },

  // 提交订单前重新计算价格
  recalculatePriceBeforeSubmit: function () {
    const that = this;

    return new Promise((resolve, reject) => {
      // 只有礼券兑换订单（type == 0）才跳过价格计算
      if (this.data.isVoucherOrder && this.data.type == 0) {
        console.log('礼券兑换订单（免费），跳过价格重新计算');
        resolve();
        return;
      }

      // 对于单商品订单，使用本地计算逻辑
      if (this.data.goodsId > 0 && this.data.type == 1) {
        console.log('单商品付费订单，使用本地优惠券计算逻辑');
        this.calculateSingleGoodsPrice();
        resolve();
        return;
      }

      // 购物车订单使用API计算
      const requestData = {
        addressId: this.data.addressId,
        couponId: this.data.couponId || 0,
        usePoints: this.data.usePointsEnabled ? this.data.usePoints : 0,
        useBalance: this.data.useBalanceEnabled ? this.data.useBalance : 0
      };

      console.log('提交前价格计算请求参数:', requestData);

      util.request(api.CartAdvancedCheckout, requestData, 'POST').then(function (res) {
        if (res.success) {
          const updateData = {
            actualPrice: res.data.actualPrice,
            couponPrice: res.data.couponPrice,
            pointsPrice: res.data.pointsPrice,
            balancePrice: res.data.balancePrice,
            orderTotalPrice: res.data.orderTotalPrice,
            goodsTotalPrice: res.data.goodsTotalPrice,
            checkedGoodsList: res.data.checkedGoodsList
          };

          console.log('提交前价格计算结果:', {
            actualPrice: res.data.actualPrice,
            goodsTotalPrice: res.data.goodsTotalPrice,
            couponPrice: res.data.couponPrice,
            pointsPrice: res.data.pointsPrice,
            balancePrice: res.data.balancePrice
          });

          that.setData(updateData);
          resolve();
        } else {
          reject(new Error(res.message || '价格计算失败'));
        }
      }).catch(function (error) {
        reject(error);
      });
    });
  },

  // 执行订单提交
  doSubmitOrder: function () {
    const that = this;

    // 获取实际的商品数量
    let actualNumber = this.data.number; // 默认使用初始数量

    // 对于购物车订单，从商品列表中获取实际数量
    if (this.data.checkedGoodsList && this.data.checkedGoodsList.length > 0) {
      // 如果是单商品订单，从商品列表中获取数量
      if (this.data.goodsId > 0) {
        const targetItem = this.data.checkedGoodsList.find(item =>
          item.goodsId === this.data.goodsId || item.id === this.data.goodsId
        );
        if (targetItem) {
          actualNumber = targetItem.number;
          console.log('单商品订单从商品列表获取实际数量:', {
            goodsId: this.data.goodsId,
            originalNumber: this.data.number,
            actualNumber: actualNumber
          });
        }
      }
    }

    const requestData = {
      addressId: this.data.addressId,
      goodsId: this.data.goodsId,
      number: actualNumber // 使用实际调整后的数量
    };

    // 如果有productId，添加到请求数据中
    if (this.data.productId && this.data.productId > 0) {
      requestData.productId = this.data.productId;
    }

    if (this.data.couponId && this.data.couponId !== 'null' && this.data.couponId > 0) {
      requestData.couponId = this.data.couponId;
    }

    // 添加积分抵扣
    if (this.data.usePointsEnabled && this.data.usePoints > 0) {
      requestData.usePoints = this.data.usePoints;
    }

    // 添加余额抵扣
    if (this.data.useBalanceEnabled && this.data.useBalance > 0) {
      requestData.useBalance = this.data.useBalance;
    }

    // 添加推广者ID
    if (this.data.promoterId) {
      requestData.promoterId = this.data.promoterId;
      console.log('订单提交包含推广者ID:', this.data.promoterId);
    }

    console.log('最终订单提交数据:', {
      requestData: requestData,
      actualPrice: this.data.actualPrice,
      goodsTotalPrice: this.data.goodsTotalPrice
    });

    // 只有明确的单商品订单才使用特殊的提交流程
    if (this.data.goodsId > 0 && (this.data.type == 0 || this.data.type == 1)) {
      requestData.goodsId = this.data.goodsId;
      requestData.type = that.data.type;
      // 使用礼券API提交订单
      util.request(api.OrderCardSubmit, requestData, 'POST').then(res => {
        wx.hideLoading();
        if (res.success) {
          const orderId = res.data.orderInfo.id;

          // 订单提交成功，清除storage中的优惠券ID，避免影响下一个订单
          wx.removeStorageSync('couponId');
          console.log('订单提交成功，清除storage中的优惠券ID');

          wx.showToast({
            title: '下单成功',
            icon: 'success'
          });

          // 礼券订单直接成功，跳转到订单详情页
          setTimeout(function () {

            if (that.data.type == 1) {
              const orderId = res.data.orderInfo.id;
              // 检查实际支付金额，如果为0则直接跳转成功页面
              if (that.data.actualPrice <= 0) {
                console.log('单商品订单实际支付金额为0，跳过微信支付直接完成订单');
                wx.redirectTo({
                  url: '/pages/payResult/payResult?status=true&orderId=' + orderId
                });
              } else {
                // 实际支付金额大于0，调用微信支付
                pay.payOrder(parseInt(orderId)).then(res => {
                  wx.redirectTo({
                    url: '/pages/payResult/payResult?status=true&orderId=' + orderId
                  });
                }).catch(res => {
                  wx.redirectTo({
                    url: '/pages/payResult/payResult?status=false&orderId=' + orderId
                  });
                });
              }
            } else {
              wx.switchTab({
                url: '/pages/ucenter/redemption/redemption'
              });
            }

          }, 1500);
        } else {
          util.showErrorToast(res.msg || '下单失败');
        }
      }).catch(function (error) {
        wx.hideLoading();
        util.showErrorToast('下单失败，请重试');
        console.error('Failed to submit order:', error);
      });
    } else {
      // 常规订单提交流程
      console.log('Regular order submit request data:', requestData);
      util.request(api.OrderSubmit, requestData, 'POST').then(res => {
        wx.hideLoading();
        if (res.success) {
          const orderId = res.data.orderInfo.id;

          // 订单提交成功，清除storage中的优惠券ID，避免影响下一个订单
          wx.removeStorageSync('couponId');
          console.log('常规订单提交成功，清除storage中的优惠券ID');

          // 检查实际支付金额，如果为0则直接跳转成功页面
          if (that.data.actualPrice <= 0) {
            console.log('实际支付金额为0，跳过微信支付直接完成订单');
            wx.redirectTo({
              url: '/pages/payResult/payResult?status=true&orderId=' + orderId
            });
          } else {
            // 实际支付金额大于0，调用微信支付
            pay.payOrder(parseInt(orderId)).then(res => {
              wx.redirectTo({
                url: '/pages/payResult/payResult?status=true&orderId=' + orderId
              });
            }).catch(res => {
              wx.redirectTo({
                url: '/pages/payResult/payResult?status=false&orderId=' + orderId
              });
            });
          }
        } else {
          util.showErrorToast('下单失败');
        }
      }).catch(function (error) {
        wx.hideLoading();
        util.showErrorToast('下单失败，请重试');
        console.error('Failed to submit order:', error);
      });
    }
  },

  // 选择优惠券
  selectCoupon() {
    wx.navigateTo({
      url: `/pages/shopping/coupon-select/coupon-select?orderAmount=${this.data.goodsTotalPrice}&couponId=${this.data.couponId || 0}`
    });
  },











  // 积分开关切换
  onPointsSwitchChange(e) {
    const enabled = e.detail.value;

    // 如果开启积分抵扣但没有可用积分，给出提示
    if (enabled && this.data.maxUsablePoints <= 0) {
      wx.showToast({
        title: '当前积分不足或不可用',
        icon: 'none',
        duration: 2000
      });
      // 重置开关状态
      this.setData({
        usePointsEnabled: false,
        usePoints: 0
      });
      return;
    }

    // 限制单次最多使用500积分
    const maxPointsPerUse = Math.min(500, this.data.maxUsablePoints);
    const usePoints = enabled ? maxPointsPerUse : 0;

    console.log('积分抵扣开关切换:', {
      enabled,
      usePoints,
      maxUsablePoints: this.data.maxUsablePoints,
      maxPointsPerUse: maxPointsPerUse,
      userPoints: this.data.userPoints,
      currentActualPrice: this.data.actualPrice
    });

    // 立即更新积分相关数据
    this.setData({
      usePointsEnabled: enabled,
      usePoints: usePoints
    });

    // 重新计算所有抵扣金额和最大可用金额
    this.recalculateAllDeductions();
  },

  // 计算积分抵扣金额
  calculatePointsPrice(points) {
    if (!points || points <= 0) return 0;
    const useRate = this.data.pointsConfig.useRate || 100;
    return parseFloat((points / useRate).toFixed(2));
  },

  // 积分输入
  onPointsInput(e) {
    const value = parseInt(e.detail.value) || 0;
    const maxUsablePoints = this.data.maxUsablePoints;
    
    // 限制单次最多使用500积分
    const maxPointsPerUse = Math.min(500, maxUsablePoints);

    // 限制输入值不超过最大可用积分或500积分
    const finalValue = Math.min(value, maxPointsPerUse);

    console.log('积分输入变化:', {
      inputValue: value,
      finalValue: finalValue,
      maxUsablePoints: maxUsablePoints,
      maxPointsPerUse: maxPointsPerUse,
      userPoints: this.data.userPoints
    });

    // 更新使用积分
    this.setData({
      usePoints: finalValue
    });

    // 使用实时更新方法，提高响应速度
    this.updateDeductionsRealtime();
  },

  // 积分输入失焦
  onPointsBlur(e) {
    let value = parseInt(e.detail.value) || 0;
    const maxPoints = this.data.maxUsablePoints;
    const userPoints = this.data.userPoints;
    const minUsePoints = this.data.pointsConfig.minUsePoints || 100;
    let showToast = false;
    let toastMessage = '';

    // 限制单次最多使用500积分
    const maxPointsPerUse = Math.min(500, maxPoints);

    // 验证输入值
    if (value > maxPointsPerUse) {
      value = maxPointsPerUse;
      showToast = true;
      toastMessage = `单次最多可使用${maxPointsPerUse}积分`;
    } else if (value > maxPoints) {
      value = maxPoints;
      showToast = true;
      toastMessage = `最多可使用${maxPoints}积分`;
    } else if (value > userPoints) {
      value = userPoints;
      showToast = true;
      toastMessage = `积分不足，最多可使用${userPoints}积分`;
    } else if (value > 0 && value < minUsePoints) {
      value = 0;
      showToast = true;
      toastMessage = `积分使用最少需要${minUsePoints}分`;
    } else if (value < 0) {
      value = 0;
    }

    console.log('积分输入失焦处理:', {
      originalValue: e.detail.value,
      processedValue: value,
      maxPoints: maxPoints,
      maxPointsPerUse: maxPointsPerUse,
      userPoints: userPoints,
      minUsePoints: minUsePoints,
      showToast: showToast,
      toastMessage: toastMessage
    });

    // 更新使用积分
    this.setData({
      usePoints: value
    });

    // 显示提示信息
    if (showToast) {
      wx.showToast({
        title: toastMessage,
        icon: 'none',
        duration: 2000
      });
    }

    // 如果积分为0，自动关闭积分抵扣
    if (value === 0) {
      this.setData({
        usePointsEnabled: false
      });
    } else if (!this.data.usePointsEnabled) {
      // 如果输入了积分但开关未开启，自动开启
      this.setData({
        usePointsEnabled: true
      });
    }

    // 重新计算所有抵扣金额
    this.recalculateAllDeductions();
  },

  // 余额开关切换
  onBalanceSwitchChange(e) {
    const enabled = e.detail.value;

    // 如果开启余额抵扣但没有可用余额，给出提示
    if (enabled && this.data.maxUsableBalance <= 0) {
      wx.showToast({
        title: '当前余额不足或不可用',
        icon: 'none',
        duration: 2000
      });
      // 重置开关状态
      this.setData({
        useBalanceEnabled: false,
        useBalance: 0
      });
      return;
    }

    const useBalance = enabled ? this.data.maxUsableBalance : 0;

    console.log('余额抵扣开关切换:', {
      enabled,
      useBalance,
      maxUsableBalance: this.data.maxUsableBalance,
      userBalance: this.data.userBalance
    });

    this.setData({
      useBalanceEnabled: enabled,
      useBalance: useBalance
    });

    // 重新计算所有抵扣金额和最大可用金额
    this.recalculateAllDeductions();
  },

  // 余额输入
  onBalanceInput(e) {
    const value = parseFloat(e.detail.value) || 0;
    const maxUsableBalance = this.data.maxUsableBalance;

    // 限制输入值不超过最大可用余额
    const finalValue = Math.min(value, maxUsableBalance);

    console.log('余额输入变化:', {
      inputValue: value,
      finalValue: finalValue,
      maxUsableBalance: maxUsableBalance,
      userBalance: this.data.userBalance
    });

    // 更新使用余额
    this.setData({
      useBalance: finalValue
    });

    // 使用实时更新方法，提高响应速度
    this.updateDeductionsRealtime();
  },

  // 余额输入失焦
  onBalanceBlur(e) {
    let value = parseFloat(e.detail.value) || 0;
    const maxBalance = this.data.maxUsableBalance;
    const userBalance = this.data.userBalance;
    let showToast = false;
    let toastMessage = '';

    // 验证输入值
    if (value > maxBalance) {
      value = maxBalance;
      showToast = true;
      toastMessage = `最多可使用${maxBalance.toFixed(2)}元`;
    } else if (value > userBalance) {
      value = userBalance;
      showToast = true;
      toastMessage = `余额不足，最多可使用${userBalance.toFixed(2)}元`;
    } else if (value < 0) {
      value = 0;
    }

    const finalValue = parseFloat(value.toFixed(2));

    console.log('余额输入失焦处理:', {
      originalValue: e.detail.value,
      processedValue: finalValue,
      maxBalance: maxBalance,
      userBalance: userBalance,
      showToast: showToast,
      toastMessage: toastMessage
    });

    // 更新使用余额
    this.setData({
      useBalance: finalValue
    });

    // 显示提示信息
    if (showToast) {
      wx.showToast({
        title: toastMessage,
        icon: 'none',
        duration: 2000
      });
    }

    // 如果余额为0，自动关闭余额抵扣
    if (finalValue === 0) {
      this.setData({
        useBalanceEnabled: false
      });
    } else if (!this.data.useBalanceEnabled) {
      // 如果输入了余额但开关未开启，自动开启
      this.setData({
        useBalanceEnabled: true
      });
    }

    // 重新计算所有抵扣金额
    this.recalculateAllDeductions();
  },

  // 使用最大余额
  useMaxBalance() {
    const maxBalance = this.data.maxUsableBalance;

    console.log('使用最大余额:', {
      maxBalance: maxBalance,
      userBalance: this.data.userBalance
    });

    this.setData({
      useBalance: maxBalance,
      useBalanceEnabled: true
    });

    // 重新计算所有抵扣金额
    this.recalculateAllDeductions();
  },

  // 使用最大积分
  useMaxPoints() {
    const maxPoints = this.data.maxUsablePoints;
    // 限制单次最多使用500积分
    const maxPointsPerUse = Math.min(500, maxPoints);

    console.log('使用最大积分:', {
      maxPoints: maxPoints,
      maxPointsPerUse: maxPointsPerUse,
      userPoints: this.data.userPoints,
      pointsValue: this.calculatePointsPrice(maxPointsPerUse)
    });

    this.setData({
      usePoints: maxPointsPerUse,
      usePointsEnabled: true
    });

    // 重新计算所有抵扣金额
    this.recalculateAllDeductions();
  },

  // 计算最大可用余额
  calculateMaxUsableBalance() {
    // 计算当前订单总价（扣除已有的优惠券和积分抵扣）
    const goodsTotalPrice = this.data.goodsTotalPrice || 0;
    const freightPrice = this.data.freightPrice || 0;
    const couponPrice = this.data.couponPrice || 0;
    const pointsPrice = this.data.pointsPrice || 0;
    const userBalance = this.data.userBalance || 0;

    // 订单总价 = 商品总价 + 运费 - 优惠券抵扣 - 积分抵扣
    const orderTotal = goodsTotalPrice + freightPrice - couponPrice - pointsPrice;

    // 最大可用余额 = min(用户余额, max(0, 订单总价))
    const maxUsableBalance = Math.min(userBalance, Math.max(0, orderTotal));

    console.log('计算最大可用余额:', {
      goodsTotalPrice,
      freightPrice,
      couponPrice,
      pointsPrice,
      userBalance,
      orderTotal,
      maxUsableBalance
    });

    return parseFloat(maxUsableBalance.toFixed(2));
  },

  // 根据给定数据计算最大可用余额（用于API返回数据处理）
  calculateMaxUsableBalanceWithData: function (data) {
    const goodsTotalPrice = data.goodsTotalPrice || 0;
    const freightPrice = data.freightPrice || 0;
    const couponPrice = data.couponPrice || 0;
    const pointsPrice = data.pointsPrice || 0;
    const userBalance = data.userBalance || 0;

    // 订单总价 = 商品总价 + 运费 - 优惠券抵扣 - 积分抵扣
    const orderTotal = goodsTotalPrice + freightPrice - couponPrice - pointsPrice;

    // 最大可用余额 = min(用户余额, max(0, 订单总价))
    const maxUsableBalance = Math.min(userBalance, Math.max(0, orderTotal));

    return parseFloat(maxUsableBalance.toFixed(2));
  },

  // 统一计算所有抵扣金额，确保总抵扣不超过订单金额
  calculateAllDeductions: function () {
    const goodsTotalPrice = this.data.goodsTotalPrice || 0;
    const freightPrice = this.data.freightPrice || 0;

    // 订单基础金额（商品总价 + 运费）
    const baseOrderAmount = goodsTotalPrice + freightPrice;

    console.log('开始统一计算抵扣金额:', {
      goodsTotalPrice,
      freightPrice,
      baseOrderAmount,
      currentState: {
        couponId: this.data.couponId,
        checkedCoupon: this.data.checkedCoupon,
        usePointsEnabled: this.data.usePointsEnabled,
        usePoints: this.data.usePoints,
        useBalanceEnabled: this.data.useBalanceEnabled,
        useBalance: this.data.useBalance
      }
    });

    // 抵扣优先级：优惠券 > 积分 > 余额
    // 这样可以最大化用户优惠

    let remainingAmount = baseOrderAmount;
    let finalCouponPrice = 0;
    let finalPointsPrice = 0;
    let finalBalancePrice = 0;
    let deductionDetails = [];

    // 1. 优惠券抵扣（优先级最高）
    if (this.data.couponId > 0 && this.data.checkedCoupon) {
      const coupon = this.data.checkedCoupon;
      if (coupon.minAmount <= goodsTotalPrice) {
        finalCouponPrice = Math.min(coupon.amount || 0, remainingAmount);
        remainingAmount = Math.max(0, remainingAmount - finalCouponPrice);
        if (finalCouponPrice > 0) {
          deductionDetails.push({
            type: 'coupon',
            name: coupon.name || '优惠券',
            amount: finalCouponPrice
          });
        }
      }
    }

    // 2. 积分抵扣（优先级中等）
    if (this.data.usePointsEnabled && this.data.usePoints > 0) {
      const requestedPointsPrice = this.calculatePointsPrice(this.data.usePoints);
      finalPointsPrice = Math.min(requestedPointsPrice, remainingAmount);
      remainingAmount = Math.max(0, remainingAmount - finalPointsPrice);
      if (finalPointsPrice > 0) {
        deductionDetails.push({
          type: 'points',
          name: `${this.data.usePoints}积分抵扣`,
          amount: finalPointsPrice
        });
      }
    }

    // 3. 余额抵扣（优先级最低）
    if (this.data.useBalanceEnabled && this.data.useBalance > 0) {
      const requestedBalancePrice = Math.min(this.data.useBalance, this.data.userBalance);
      finalBalancePrice = Math.min(requestedBalancePrice, remainingAmount);
      remainingAmount = Math.max(0, remainingAmount - finalBalancePrice);
      if (finalBalancePrice > 0) {
        deductionDetails.push({
          type: 'balance',
          name: '余额抵扣',
          amount: finalBalancePrice
        });
      }
    }

    // 计算最终实付金额
    const finalActualPrice = Math.max(0, remainingAmount);
    const totalDeduction = finalCouponPrice + finalPointsPrice + finalBalancePrice;

    // 计算实际使用的积分数量（基于实际抵扣金额反推）
    let actualUsePoints = this.data.usePoints;
    if (finalPointsPrice > 0 && this.data.pointsConfig && this.data.pointsConfig.useRate) {
      actualUsePoints = Math.round(finalPointsPrice * this.data.pointsConfig.useRate);
    }

    const result = {
      couponPrice: parseFloat(finalCouponPrice.toFixed(2)),
      pointsPrice: parseFloat(finalPointsPrice.toFixed(2)),
      balancePrice: parseFloat(finalBalancePrice.toFixed(2)),
      orderTotalPrice: parseFloat((baseOrderAmount - totalDeduction).toFixed(2)),
      actualPrice: parseFloat(finalActualPrice.toFixed(2)),
      totalDeduction: parseFloat(totalDeduction.toFixed(2)),
      deductionDetails: deductionDetails,
      savingsInfo: {
        totalSavings: totalDeduction,
        deductionCount: deductionDetails.length,
        savingsPercentage: baseOrderAmount > 0 ? ((totalDeduction / baseOrderAmount) * 100).toFixed(1) : 0
      },
      // 添加实际使用的金额，确保显示一致性
      actualUseBalance: parseFloat(finalBalancePrice.toFixed(2)),
      actualUsePoints: actualUsePoints
    };

    console.log('统一抵扣计算结果:', {
      baseOrderAmount,
      ...result,
      '抵扣是否超额': result.totalDeduction > baseOrderAmount,
      '抵扣明细': deductionDetails
    });

    return result;
  },

  // 重新计算并更新最大可用金额
  updateMaxUsableAmounts: function () {
    // 重新计算最大可用余额和积分，但不相互限制
    const goodsTotalPrice = this.data.goodsTotalPrice || 0;
    const freightPrice = this.data.freightPrice || 0;
    const baseOrderAmount = goodsTotalPrice + freightPrice;

    // 重新计算最大可用积分（基于订单总额，不受其他抵扣影响）
    let newMaxUsablePoints = 0;
    if (this.data.pointsConfig && this.data.userPoints > 0) {
      const maxUseRatio = this.data.pointsConfig.maxUseRatio || 100;
      const maxDeductAmount = baseOrderAmount * (maxUseRatio / 100);
      const maxPointsByAmount = Math.floor(maxDeductAmount * this.data.pointsConfig.useRate);
      // 限制单次最多使用500积分
      newMaxUsablePoints = Math.min(500, this.data.userPoints, maxPointsByAmount);
      if (this.data.userPoints < this.data.pointsConfig.minUsePoints) {
        newMaxUsablePoints = 0;
      }
    }

    // 重新计算最大可用余额（基于订单总额，不受其他抵扣影响）
    const newMaxUsableBalance = Math.min(this.data.userBalance || 0, baseOrderAmount);

    // 更新数据
    this.setData({
      maxUsablePoints: newMaxUsablePoints,
      maxUsableBalance: parseFloat(newMaxUsableBalance.toFixed(2))
    });

    console.log('更新最大可用金额（独立计算）:', {
      baseOrderAmount,
      userPoints: this.data.userPoints,
      userBalance: this.data.userBalance,
      newMaxUsablePoints,
      newMaxUsableBalance,
      pointsConfig: this.data.pointsConfig
    });
  },

  // 统一的重新计算方法
  recalculateAllDeductions: function () {
    console.log('开始重新计算所有抵扣金额', {
      currentState: {
        useBalanceEnabled: this.data.useBalanceEnabled,
        useBalance: this.data.useBalance,
        usePointsEnabled: this.data.usePointsEnabled,
        usePoints: this.data.usePoints,
        couponId: this.data.couponId
      }
    });

    // 先更新最大可用金额
    this.updateMaxUsableAmounts();

    // 如果是单商品订单，使用本地计算
    if (this.data.goodsId > 0 && this.data.type == 1) {
      this.calculateSingleGoodsPrice();
    } else {
      // 购物车订单调用API重新计算
      this.recalculatePrice();
    }

    // 显示组合抵扣提示
    this.showCombinedDiscountTip();
  },

  // 实时更新抵扣金额（用于输入过程中的实时计算）
  updateDeductionsRealtime: function () {
    // 使用本地计算逻辑进行实时更新，避免频繁API调用
    const deductionResult = this.calculateAllDeductions();

    // 更新页面数据，确保显示值与实际抵扣金额一致
    const updateData = {
      couponPrice: deductionResult.couponPrice,
      pointsPrice: deductionResult.pointsPrice,
      balancePrice: deductionResult.balancePrice,
      orderTotalPrice: deductionResult.orderTotalPrice,
      actualPrice: deductionResult.actualPrice
    };

    // 如果实际使用的余额与输入的余额不一致，更新显示值
    if (deductionResult.actualUseBalance !== undefined &&
      Math.abs(deductionResult.actualUseBalance - this.data.useBalance) > 0.01) {
      updateData.useBalance = deductionResult.actualUseBalance;
      console.log('余额显示值调整:', {
        原始输入: this.data.useBalance,
        实际使用: deductionResult.actualUseBalance,
        抵扣金额: deductionResult.balancePrice
      });
    }

    // 如果实际使用的积分与输入的积分不一致，更新显示值
    if (deductionResult.actualUsePoints !== undefined &&
      deductionResult.actualUsePoints !== this.data.usePoints) {
      updateData.usePoints = deductionResult.actualUsePoints;
      console.log('积分显示值调整:', {
        原始输入: this.data.usePoints,
        实际使用: deductionResult.actualUsePoints,
        抵扣金额: deductionResult.pointsPrice
      });
    }

    this.setData(updateData);

    console.log('实时更新抵扣金额:', {
      ...deductionResult,
      更新的数据: updateData
    });
  },

  // 显示组合抵扣提示
  showCombinedDiscountTip: function () {
    const discountCount = (this.data.couponPrice > 0 ? 1 : 0) +
      (this.data.pointsPrice > 0 ? 1 : 0) +
      (this.data.balancePrice > 0 ? 1 : 0);

    if (discountCount > 1) {
      const totalSaved = this.data.couponPrice + this.data.pointsPrice + this.data.balancePrice;
      const baseAmount = this.data.goodsTotalPrice + this.data.freightPrice;
      const savingsPercentage = baseAmount > 0 ? ((totalSaved / baseAmount) * 100).toFixed(1) : 0;

      console.log(`组合抵扣：使用了${discountCount}种优惠方式，共节省${totalSaved.toFixed(2)}元（${savingsPercentage}%）`);

      // 显示组合抵扣成功提示
      let tipMessage = '';
      if (discountCount === 3) {
        tipMessage = `🎉 已使用全部优惠！共节省¥${totalSaved.toFixed(2)}`;
      } else if (discountCount === 2) {
        tipMessage = `💰 组合优惠生效！节省¥${totalSaved.toFixed(2)}`;
      }

      if (tipMessage) {
        wx.showToast({
          title: tipMessage,
          icon: 'none',
          duration: 2500
        });
      }

      // 更新组合抵扣信息到页面数据
      this.setData({
        combinedDiscountInfo: {
          isActive: true,
          count: discountCount,
          totalSavings: totalSaved,
          savingsPercentage: savingsPercentage,
          details: this.generateDiscountSummary()
        }
      });
    } else {
      // 清除组合抵扣信息
      this.setData({
        combinedDiscountInfo: {
          isActive: false,
          count: 0,
          totalSavings: 0,
          savingsPercentage: 0,
          details: []
        }
      });
    }
  },

  // 生成抵扣摘要信息
  generateDiscountSummary: function () {
    const summary = [];

    if (this.data.couponPrice > 0) {
      summary.push({
        type: 'coupon',
        name: this.data.checkedCoupon?.name || '优惠券',
        amount: this.data.couponPrice,
        icon: '🎫'
      });
    }

    if (this.data.pointsPrice > 0) {
      summary.push({
        type: 'points',
        name: `${this.data.usePoints}积分`,
        amount: this.data.pointsPrice,
        icon: '⭐'
      });
    }

    if (this.data.balancePrice > 0) {
      summary.push({
        type: 'balance',
        name: '余额',
        amount: this.data.balancePrice,
        icon: '💰'
      });
    }

    return summary;
  },

  // 智能推荐抵扣方式
  recommendDiscountOptions: function () {
    const recommendations = [];

    // 检查是否有可用但未使用的优惠券
    if (!this.data.checkedCoupon && this.data.availableCoupons && this.data.availableCoupons.length > 0) {
      const bestCoupon = this.data.availableCoupons.reduce((best, current) => {
        return (current.amount > best.amount) ? current : best;
      });
      recommendations.push({
        type: 'coupon',
        message: `您有可用优惠券，最高可省${bestCoupon.amount}元`,
        action: '选择优惠券'
      });
    }

    // 检查是否有可用但未使用的积分
    if (!this.data.usePointsEnabled && this.data.userPoints > 0 && this.data.maxUsablePoints > 0) {
      const pointsValue = this.calculatePointsPrice(this.data.maxUsablePoints);
      recommendations.push({
        type: 'points',
        message: `您有${this.data.userPoints}积分，可抵扣${pointsValue.toFixed(2)}元`,
        action: '使用积分'
      });
    }

    // 检查是否有可用但未使用的余额
    if (!this.data.useBalanceEnabled && this.data.userBalance > 0 && this.data.maxUsableBalance > 0) {
      recommendations.push({
        type: 'balance',
        message: `您有余额${this.data.userBalance.toFixed(2)}元，可抵扣${this.data.maxUsableBalance.toFixed(2)}元`,
        action: '使用余额'
      });
    }

    return recommendations;
  },

  // 显示抵扣推荐
  showDiscountRecommendations: function () {
    const recommendations = this.recommendDiscountOptions();

    if (recommendations.length > 0) {
      console.log('抵扣推荐:', recommendations);

      // 可以在这里实现推荐弹窗或提示
      // 例如：显示一个模态框让用户选择是否使用推荐的抵扣方式
    }
  },

  // 一键使用所有可用抵扣
  useAllAvailableDiscounts: function () {
    let hasChanges = false;
    let changesDetail = [];

    // 显示加载提示
    wx.showLoading({
      title: '正在优化优惠...',
      mask: true
    });

    // 1. 自动选择最优优惠券
    if (!this.data.checkedCoupon && this.data.availableCoupons && this.data.availableCoupons.length > 0) {
      const bestCoupon = this.data.availableCoupons.reduce((best, current) => {
        // 优先选择金额大的优惠券，如果金额相同则选择门槛低的
        if (current.amount > best.amount) {
          return current;
        } else if (current.amount === best.amount) {
          return (current.minAmount || 0) < (best.minAmount || 0) ? current : best;
        }
        return best;
      });

      // 验证优惠券是否满足使用条件
      if ((bestCoupon.minAmount || 0) <= this.data.goodsTotalPrice) {
        this.setData({
          checkedCoupon: bestCoupon,
          couponId: bestCoupon.id
        });
        changesDetail.push(`选择优惠券：${bestCoupon.name}（-¥${bestCoupon.amount}）`);
        hasChanges = true;
      }
    }

    // 2. 启用积分抵扣
    if (!this.data.usePointsEnabled && this.data.maxUsablePoints > 0) {
      const pointsValue = this.calculatePointsPrice(this.data.maxUsablePoints);
      this.setData({
        usePointsEnabled: true,
        usePoints: this.data.maxUsablePoints
      });
      changesDetail.push(`使用积分：${this.data.maxUsablePoints}分（-¥${pointsValue.toFixed(2)}）`);
      hasChanges = true;
    }

    // 3. 启用余额抵扣
    if (!this.data.useBalanceEnabled && this.data.maxUsableBalance > 0) {
      this.setData({
        useBalanceEnabled: true,
        useBalance: this.data.maxUsableBalance
      });
      changesDetail.push(`使用余额：¥${this.data.maxUsableBalance.toFixed(2)}`);
      hasChanges = true;
    }

    // 延迟执行，确保数据更新完成
    setTimeout(() => {
      wx.hideLoading();

      if (hasChanges) {
        // 重新计算抵扣金额
        this.recalculateAllDeductions();

        // 显示详细的优化结果
        const totalOptimizations = changesDetail.length;
        let successMessage = `🎉 已为您开启${totalOptimizations}项优惠`;

        wx.showModal({
          title: '优惠优化完成',
          content: changesDetail.join('\n'),
          showCancel: false,
          confirmText: '知道了',
          confirmColor: '#667eea'
        });

        // 记录用户使用一键优惠的行为
        console.log('一键优惠使用详情:', {
          optimizationCount: totalOptimizations,
          details: changesDetail,
          timestamp: new Date().toISOString()
        });

      } else {
        // 分析为什么没有可用优惠
        let reasons = [];

        if (this.data.checkedCoupon) {
          reasons.push('已选择优惠券');
        } else if (!this.data.availableCoupons || this.data.availableCoupons.length === 0) {
          reasons.push('暂无可用优惠券');
        }

        if (this.data.usePointsEnabled) {
          reasons.push('已启用积分抵扣');
        } else if (this.data.maxUsablePoints <= 0) {
          reasons.push('积分不足或不可用');
        }

        if (this.data.useBalanceEnabled) {
          reasons.push('已启用余额抵扣');
        } else if (this.data.maxUsableBalance <= 0) {
          reasons.push('余额不足或不可用');
        }

        wx.showModal({
          title: '优惠状态',
          content: reasons.length > 0 ? reasons.join('，') : '已使用所有可用优惠',
          showCancel: false,
          confirmText: '知道了',
          confirmColor: '#667eea'
        });
      }
    }, 500);
  },

  // 重新计算价格
  recalculatePrice() {
    const that = this;

    // 只有礼券兑换订单（type == 0）才跳过价格计算
    // 付费商品订单（type == 1）和普通购物车结算都需要价格计算
    console.log('订单类型检查:', {
      isVoucherOrder: this.data.isVoucherOrder,
      type: this.data.type,
      goodsId: this.data.goodsId,
      '是否跳过计算': this.data.isVoucherOrder && this.data.type == 0
    });

    if (this.data.isVoucherOrder && this.data.type == 0) {
      console.log('礼券兑换订单（免费），跳过价格重新计算');
      return;
    }

    // 对于单商品订单，使用本地计算逻辑
    if (this.data.goodsId > 0 && this.data.type == 1) {
      console.log('单商品付费订单，使用本地优惠券计算逻辑');
      this.calculateSingleGoodsPrice();
      return;
    }

    // 购物车订单使用API计算
    const requestData = {
      addressId: this.data.addressId,
      couponId: this.data.couponId || 0,
      usePoints: this.data.usePointsEnabled ? this.data.usePoints : 0,
      useBalance: this.data.useBalanceEnabled ? this.data.useBalance : 0
    };

    console.log('重新计算价格请求参数:', requestData);

    wx.showLoading({
      title: '计算中...',
    });

    util.request(api.CartAdvancedCheckout, requestData, 'POST').then(function (res) {
      wx.hideLoading();
      if (res.success) {
        const updateData = {
          actualPrice: res.data.actualPrice,
          couponPrice: res.data.couponPrice,
          pointsPrice: res.data.pointsPrice,
          balancePrice: res.data.balancePrice,
          orderTotalPrice: res.data.orderTotalPrice,
          maxUsablePoints: res.data.maxUsablePoints,
          maxUsableBalance: that.calculateMaxUsableBalance()
        };

        // 确保使用余额显示值与实际抵扣金额一致
        if (res.data.balancePrice !== undefined && res.data.balancePrice > 0) {
          updateData.useBalance = res.data.balancePrice;
          console.log('API返回余额抵扣，同步显示值:', {
            原始使用余额: that.data.useBalance,
            实际抵扣金额: res.data.balancePrice,
            更新显示值: res.data.balancePrice
          });
        }

        // 确保使用积分显示值与实际抵扣金额一致
        if (res.data.pointsPrice !== undefined && res.data.pointsPrice > 0 && that.data.pointsConfig && that.data.pointsConfig.useRate) {
          const actualUsePoints = Math.round(res.data.pointsPrice * that.data.pointsConfig.useRate);
          updateData.usePoints = actualUsePoints;
          console.log('API返回积分抵扣，同步显示值:', {
            原始使用积分: that.data.usePoints,
            实际抵扣金额: res.data.pointsPrice,
            计算使用积分: actualUsePoints,
            更新显示值: actualUsePoints
          });
        }

        // 如果后端返回了优惠券信息，更新checkedCoupon
        if (res.data.checkedCoupon) {
          updateData.checkedCoupon = res.data.checkedCoupon;
          // 同时保存优惠券ID到storage，确保数据持久化
          if (res.data.checkedCoupon.id) {
            wx.setStorageSync('couponId', parseInt(res.data.checkedCoupon.id));
            console.log('保存后端返回的优惠券ID到storage:', res.data.checkedCoupon.id);
          }
        }

        that.setData(updateData);
        console.log('重新计算价格后更新数据:', updateData);
        console.log('更新后的页面数据:', {
          actualPrice: that.data.actualPrice,
          couponPrice: that.data.couponPrice,
          checkedCoupon: that.data.checkedCoupon
        });
      } else {
        console.error('重新计算价格失败:', res.msg);
        util.showErrorToast('价格计算失败');
      }
    }).catch(function (error) {
      wx.hideLoading();
      console.error('重新计算价格失败:', error);
      util.showErrorToast('网络错误，请重试');
    });
  },

  // 计算单商品订单价格（使用统一的抵扣计算逻辑）
  calculateSingleGoodsPrice() {
    console.log('开始计算单商品订单价格:', {
      goodsTotalPrice: this.data.goodsTotalPrice,
      couponId: this.data.couponId,
      checkedCoupon: this.data.checkedCoupon,
      usePointsEnabled: this.data.usePointsEnabled,
      usePoints: this.data.usePoints,
      useBalanceEnabled: this.data.useBalanceEnabled,
      useBalance: this.data.useBalance
    });

    // 使用统一的抵扣计算方法
    const deductionResult = this.calculateAllDeductions();

    // 准备更新数据，确保显示值与实际抵扣金额一致
    const updateData = {
      couponPrice: deductionResult.couponPrice,
      pointsPrice: deductionResult.pointsPrice,
      balancePrice: deductionResult.balancePrice,
      orderTotalPrice: deductionResult.orderTotalPrice,
      actualPrice: deductionResult.actualPrice
    };

    // 确保使用余额显示值与实际抵扣金额一致
    if (deductionResult.actualUseBalance !== undefined) {
      updateData.useBalance = deductionResult.actualUseBalance;
    }

    // 确保使用积分显示值与实际抵扣金额一致
    if (deductionResult.actualUsePoints !== undefined) {
      updateData.usePoints = deductionResult.actualUsePoints;
    }

    // 更新页面数据
    this.setData(updateData);

    // 重新计算最大可用金额
    this.updateMaxUsableAmounts();

    console.log('单商品订单价格计算完成:', {
      ...deductionResult,
      更新的数据: updateData
    });

    wx.showToast({
      title: '价格已更新',
      icon: 'success',
      duration: 1000
    });
  },

  // 减少商品数量
  decreaseQuantity: function (e) {
    const index = e.currentTarget.dataset.index;
    const cartId = e.currentTarget.dataset.cartId;
    const goodsId = e.currentTarget.dataset.goodsId;
    const productId = e.currentTarget.dataset.productId;

    let checkedGoodsList = [...this.data.checkedGoodsList];
    const currentItem = checkedGoodsList[index];

    if (currentItem.number <= 1) {
      wx.showToast({
        title: '商品数量不能少于1件',
        icon: 'none',
        duration: 1500
      });
      return;
    }

    // 更新本地数量
    currentItem.number = currentItem.number - 1;
    checkedGoodsList[index] = currentItem;

    // 立即更新界面显示，包括小计价格
    this.updateLocalPrices(checkedGoodsList);

    // 更新购物车数量并重新计算价格
    this.updateCartQuantity(cartId, goodsId, productId, currentItem.number, index);
  },

  // 增加商品数量
  increaseQuantity: function (e) {
    const index = e.currentTarget.dataset.index;
    const cartId = e.currentTarget.dataset.cartId;
    const goodsId = e.currentTarget.dataset.goodsId;
    const productId = e.currentTarget.dataset.productId;

    let checkedGoodsList = [...this.data.checkedGoodsList];
    const currentItem = checkedGoodsList[index];

    // 检查库存限制（如果有的话）
    const maxQuantity = currentItem.maxQuantity || 99;
    if (currentItem.number >= maxQuantity) {
      wx.showToast({
        title: `商品数量不能超过${maxQuantity}件`,
        icon: 'none',
        duration: 1500
      });
      return;
    }

    // 更新本地数量
    currentItem.number = currentItem.number + 1;
    checkedGoodsList[index] = currentItem;

    // 立即更新界面显示，包括小计价格
    this.updateLocalPrices(checkedGoodsList);

    // 更新购物车数量并重新计算价格
    this.updateCartQuantity(cartId, goodsId, productId, currentItem.number, index);
  },

  // 更新本地价格显示
  updateLocalPrices: function (checkedGoodsList) {
    // 计算商品总价
    let goodsTotalPrice = 0;
    checkedGoodsList.forEach(item => {
      goodsTotalPrice += item.retailPrice * item.number;
    });

    // 如果是单商品订单，同步更新number字段
    let updateData = {
      checkedGoodsList: checkedGoodsList,
      goodsTotalPrice: goodsTotalPrice.toFixed(2)
    };

    if (this.data.goodsId > 0 && checkedGoodsList.length === 1) {
      updateData.number = checkedGoodsList[0].number;
      console.log('单商品订单同步更新number字段:', {
        goodsId: this.data.goodsId,
        oldNumber: this.data.number,
        newNumber: checkedGoodsList[0].number
      });
    }

    // 更新数据，触发界面重新渲染
    this.setData(updateData);

    // 重新计算最大可用金额
    this.updateMaxUsableAmounts();
  },



  // 更新购物车商品数量
  updateCartQuantity: function (cartId, goodsId, productId, quantity, index) {
    const that = this;

    // 显示加载提示
    wx.showLoading({
      title: '更新中...',
      mask: true
    });

    // 调用更新购物车数量的API，使用正确的参数格式
    util.request(api.CartUpdate, {
      id: cartId,           // 购物车项ID（必需）
      goodsId: goodsId,     // 商品ID
      productId: productId || 0,  // 产品ID
      number: quantity      // 数量
    }, 'POST').then(function (res) {
      wx.hideLoading();

      if (res.success) {
        // 更新成功，重新计算价格
        that.recalculatePrice();

        // 显示成功提示
        wx.showToast({
          title: '已更新',
          icon: 'success',
          duration: 1000
        });

        console.log('购物车数量更新成功:', {
          cartId: cartId,
          quantity: quantity,
          goodsId: goodsId
        });
      } else {
        // 更新失败，恢复原数量
        wx.showToast({
          title: res.message || '更新失败',
          icon: 'none',
          duration: 2000
        });

        // 重新获取购物车信息以恢复正确状态
        that.getCheckoutInfo();
      }
    }).catch(function (error) {
      wx.hideLoading();
      console.error('更新购物车数量失败:', error);

      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none',
        duration: 2000
      });

      // 重新获取购物车信息以恢复正确状态
      that.getCheckoutInfo();
    });
  },

  // 更新运费提醒信息
  updateFreightNotice: function () {
    const that = this;
    const freightPrice = that.data.freightPrice || 0;
    const checkedAddress = that.data.checkedAddress;

    // 特殊地区列表
    const specialRegions = ['香港', '澳门', '台湾', '新疆', '西藏', '海南'];

    let showNotice = false;
    let noticeText = '';

    if (checkedAddress && checkedAddress.provinceName) {
      const provinceName = checkedAddress.provinceName;

      // 检查是否为特殊地区
      const isSpecialRegion = specialRegions.some(region =>
        provinceName.includes(region) || region.includes(provinceName)
      );

      if (isSpecialRegion && freightPrice > 0) {
        showNotice = true;
        noticeText = `收货地址为${provinceName}，需加收¥${freightPrice}运费`;
      } else if (isSpecialRegion && freightPrice === 0) {
        // 特殊地区但运费为0的情况（可能是系统异常）
        showNotice = true;
        noticeText = `收货地址为${provinceName}，通常需加收运费`;
      } else if (!isSpecialRegion && freightPrice > 0) {
        // 普通地区但有运费的情况
        showNotice = true;
        noticeText = `运费：¥${freightPrice}`;
      }
    } else if (freightPrice > 0) {
      // 没有地址信息但有运费
      showNotice = true;
      noticeText = `运费：¥${freightPrice}`;
    }

    // 如果没有运费且不是特殊地区，显示免运费提醒
    if (!showNotice && freightPrice === 0 && checkedAddress && checkedAddress.provinceName) {
      const provinceName = checkedAddress.provinceName;
      const isSpecialRegion = specialRegions.some(region =>
        provinceName.includes(region) || region.includes(provinceName)
      );

      if (!isSpecialRegion) {
        showNotice = true;
        noticeText = '该地区享受免运费服务';
      }
    }

    that.setData({
      showFreightNotice: showNotice,
      freightNoticeText: noticeText
    });
  }
})
