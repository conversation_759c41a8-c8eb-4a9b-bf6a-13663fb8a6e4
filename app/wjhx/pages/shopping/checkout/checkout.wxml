<wxs src="../../../utils/format.wxs" module="format" />
<view class="page-wrapper">
    <!-- 顶部区域 -->
    <view class="header-section">
        <!-- 背景装饰 -->
        <view class="header-decoration">
            <view class="deco-shape shape-1"></view>
            <view class="deco-shape shape-2"></view>
        </view>
        <!-- 页面标题 -->
        <view class="page-title">
            <text class="title-text">确认订单</text>
            <text class="title-subtitle">Order Confirmation</text>
        </view>
    </view>
    <!-- 主要内容区域 -->
    <view class="main-content">
        <!-- 收货地址卡片 -->
        <view class="address-card">
            <view class="card-header">
                <view class="header-icon">📍</view>
                <text class="header-title">收货地址</text>
            </view>
            <!-- 已选择地址 -->
            <view class="address-content" bindtap="selectAddress" wx:if="{{checkedAddress && checkedAddress.id > 0}}">
                <view class="address-info">
                    <view class="recipient-info">
                        <text class="recipient-name">{{checkedAddress.name}}</text>
                        <text class="recipient-phone">{{checkedAddress.mobile}}</text>
                        <view class="default-badge" wx:if="{{checkedAddress.isDefault == 1}}">
                            默认
                        </view>
                    </view>
                    <view class="address-detail">
                        <text class="address-text">
                            {{checkedAddress.fullRegion + checkedAddress.address}}
                        </text>
                    </view>
                </view>
                <view class="address-arrow">
                    <text class="arrow-icon">›</text>
                </view>
            </view>
            <!-- 空地址状态 -->
            <view class="address-empty" bindtap="addAddress" wx:if="{{!checkedAddress || checkedAddress.id <= 0}}">
                <view class="empty-icon">📍</view>
                <view class="empty-content">
                    <text class="empty-title">还没有收货地址</text>
                    <text class="empty-desc">点击添加收货地址</text>
                </view>
                <view class="add-btn">
                    <text class="btn-text">添加</text>
                </view>
            </view>
        </view>
        <!-- 优惠券卡片 -->
        <view class="coupon-card">
            <view class="card-header">
                <view class="header-icon">🎫</view>
                <text class="header-title">优惠券</text>
            </view>
            <view class="coupon-content" bindtap="selectCoupon">
                <view class="coupon-info">
                    <text class="coupon-title" wx:if="{{!checkedCoupon || !checkedCoupon.id}}">
                        请选择优惠券
                    </text>
                    <text class="coupon-title selected" wx:if="{{checkedCoupon && checkedCoupon.id}}">
                        {{checkedCoupon.name || checkedCoupon.title}}
                    </text>
                    <text class="coupon-count">
                        {{availableCoupons ? availableCoupons.length : 0}}张可用
                    </text>
                </view>
                <view class="coupon-arrow">
                    <text class="arrow-icon">›</text>
                </view>
            </view>
        </view>
        <!-- 积分抵扣卡片 -->
        <view class="points-card" wx:if="{{userPoints > 0}}">
            <view class="card-header">
                <view class="header-icon">⭐</view>
                <text class="header-title">积分抵扣</text>
                <text class="points-balance">可用积分：{{userPoints}}</text>
            </view>
            <view class="points-content">
                <view class="points-switch">
                    <text class="switch-label">使用积分抵扣</text>
                    <switch checked="{{usePointsEnabled}}" bindchange="onPointsSwitchChange" color="#667eea" />
                </view>
                <!-- <view class="points-info" wx:if="{{usePointsEnabled}}">
                    <view class="points-input-section">
                        <view class="points-input-wrapper">
                            <text class="input-label">使用积分：</text>
                            <input class="points-input" type="number" value="{{usePoints}}" placeholder="最多可用{{maxUsablePoints}}" bindinput="onPointsInput" bindblur="onPointsBlur" />
                            <text class="input-unit">分</text>
                        </view>
                        <view class="points-tips">
                            <text class="tip-text">积分抵扣{{format.formatPrice(pointsPrice)}}元（{{pointsConfig.useRate}}积分=1元）</text>
                            <text class="max-use-btn" bindtap="useMaxPoints" wx:if="{{maxUsablePoints > usePoints}}">
                                使用最大
                            </text>
                        </view>
                    </view>
                </view> -->
                <view class="points-disabled-info" wx:if="{{!usePointsEnabled && maxUsablePoints <= 0}}">
                    <text class="disabled-text">当前订单暂不可使用积分抵扣</text>
                </view>
            </view>
        </view>
        <!-- 余额抵扣卡片 -->
        <view class="balance-card" wx:if="{{userBalance > 0}}">
            <view class="card-header">
                <view class="header-icon">💰</view>
                <text class="header-title">余额抵扣</text>
                <text class="balance-amount">可用余额：¥{{format.formatPrice(userBalance)}}</text>
            </view>
            <view class="balance-content">
                <view class="balance-switch">
                    <text class="switch-label">使用余额抵扣</text>
                    <switch checked="{{useBalanceEnabled}}" bindchange="onBalanceSwitchChange" color="#667eea" />
                </view>
                <view class="balance-input-section" wx:if="{{useBalanceEnabled}}">
                    <view class="balance-input-wrapper">
                        <text class="input-label">使用余额：</text>
                        <input class="balance-input" type="digit" value="{{useBalance}}" placeholder="最多可用{{format.formatPrice(maxUsableBalance)}}" bindinput="onBalanceInput" bindblur="onBalanceBlur" />
                        <text class="input-unit">元</text>
                    </view>
                    <view class="balance-tips">
                        <text class="tip-text">余额抵扣{{format.formatPrice(balancePrice)}}元</text>
                        <text class="max-use-btn" bindtap="useMaxBalance">使用最大</text>
                    </view>
                </view>
            </view>
        </view>
        <!-- 商品列表卡片 -->
        <view class="goods-card">
            <view class="card-header">
                <view class="header-icon">📦</view>
                <text class="header-title">商品清单</text>
                <text class="goods-count">{{checkedGoodsList.length}}件商品</text>
            </view>
            <view class="goods-list">
                <view class="goods-item" wx:for="{{checkedGoodsList}}" wx:key="{{item.id}}">
                    <view class="goods-image-wrapper">
                        <image class="goods-image" src="{{format.formatImageUrl(item.listPicUrl)}}" mode="aspectFill" lazy-load="true" />
                    </view>
                    <view class="goods-info">
                        <view class="goods-header">
                            <text class="goods-name">{{item.goodsName}}</text>
                            <view class="goods-quantity-control">
                                <view class="quantity-btn quantity-minus {{item.number <= 1 ? 'disabled' : ''}}" bindtap="decreaseQuantity" data-index="{{index}}" data-cart-id="{{item.id}}" data-goods-id="{{item.goodsId}}" data-product-id="{{item.productId}}">
                                    -
                                </view>
                                <view class="quantity-display">{{item.number}}</view>
                                <view class="quantity-btn quantity-plus" bindtap="increaseQuantity" data-index="{{index}}" data-cart-id="{{item.id}}" data-goods-id="{{item.goodsId}}" data-product-id="{{item.productId}}">
                                    +
                                </view>
                            </view>
                        </view>
                        <view class="goods-spec" wx:if="{{item.goodsSpecifitionNameValue}}">
                            {{item.goodsSpecifitionNameValue}}
                        </view>
                        <view class="goods-price-info">
                            <text class="goods-price">¥{{item.retailPrice}}</text>
                            <text class="goods-subtotal">
                                小计: ¥{{format.formatPrice(item.retailPrice * item.number)}}
                            </text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <!-- 一键优惠按钮 -->
        <view class="quick-discount-card" wx:if="{{userBalance > 0 || userPoints > 0 || (availableCoupons && availableCoupons.length > 0)}}">
            <view class="quick-discount-content">
                <view class="quick-discount-info">
                    <text class="quick-discount-title">💡 智能优惠</text>
                    <text class="quick-discount-desc" wx:if="{{!combinedDiscountInfo || !combinedDiscountInfo.isActive}}">
                        一键使用所有可用优惠
                    </text>
                    <text class="quick-discount-desc" wx:if="{{combinedDiscountInfo && combinedDiscountInfo.isActive}}">
                        重新优化优惠组合
                    </text>
                </view>
                <view class="quick-discount-button" bindtap="useAllAvailableDiscounts">
                    <text class="button-text" wx:if="{{!combinedDiscountInfo || !combinedDiscountInfo.isActive}}">
                        一键优惠
                    </text>
                    <text class="button-text" wx:if="{{combinedDiscountInfo && combinedDiscountInfo.isActive}}">
                        重新优化
                    </text>
                </view>
            </view>
        </view>
        <!-- 费用明细卡片 -->
        <view class="price-card">
            <view class="card-header">
                <view class="header-icon">💰</view>
                <text class="header-title">费用明细</text>
            </view>
            <view class="price-details">
                <view class="price-item">
                    <text class="price-label">商品合计</text>
                    <text class="price-value">¥{{goodsTotalPrice}}</text>
                </view>
                <view class="price-item">
                    <text class="price-label">运费</text>
                    <text class="price-value">¥{{freightPrice}}</text>
                </view>
                <!-- 运费提醒 -->
                <view class="freight-notice" wx:if="{{showFreightNotice}}">
                    <view class="notice-icon">ℹ️</view>
                    <text class="notice-text">{{freightNoticeText}}</text>
                </view>
                <view class="price-item discount-item" wx:if="{{couponPrice > 0}}">
                    <text class="price-label">优惠券</text>
                    <text class="price-value discount-value">
                        -¥{{format.formatPrice(couponPrice)}}
                    </text>
                </view>
                <view class="price-item discount-item" wx:if="{{pointsPrice > 0}}">
                    <text class="price-label">积分抵扣</text>
                    <text class="price-value discount-value">
                        -¥{{format.formatPrice(pointsPrice)}}
                    </text>
                </view>
                <view class="price-item discount-item" wx:if="{{balancePrice > 0}}">
                    <text class="price-label">余额抵扣</text>
                    <text class="price-value discount-value">
                        -¥{{format.formatPrice(balancePrice)}}
                    </text>
                </view>
                <!-- 组合抵扣提示 -->
                <view class="combo-discount-tip" wx:if="{{(couponPrice > 0 ? 1 : 0) + (pointsPrice > 0 ? 1 : 0) + (balancePrice > 0 ? 1 : 0) > 1}}">
                    <text class="tip-icon">🎉</text>
                    <text class="tip-text">
                        已为您组合使用{{(couponPrice > 0 ? 1 : 0) + (pointsPrice > 0 ? 1 : 0) + (balancePrice > 0 ? 1 : 0)}}种优惠，
                        共节省¥{{format.formatPrice(couponPrice + pointsPrice + balancePrice)}}
                    </text>
                </view>
                <view class="price-divider"></view>
                <view class="price-item total-item">
                    <text class="price-label">实付金额</text>
                    <text class="price-value total-value">¥{{actualPrice}}</text>
                </view>
            </view>
        </view>
    </view>
    <!-- 底部支付栏 -->
    <view class="payment-bar">
        <view class="payment-info">
            <view class="total-price">
                <text class="price-label">实付金额</text>
                <view class="price-amount">
                    <text class="currency-symbol">¥</text>
                    <text class="amount-value">{{actualPrice}}</text>
                </view>
            </view>
        </view>
        <view class="payment-button" bindtap="submitOrder">
            <text class="button-text">立即支付</text>
        </view>
    </view>
</view>