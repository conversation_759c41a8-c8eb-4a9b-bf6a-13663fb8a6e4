<!-- pages/user/user.wxml -->
<view class="ns"></view>
<view class='container'>
  <!-- 用户信息 -->
  <view class='userinfo'>
    <view class='userinfo-avatar' wx:if="{{hasLogin}}">
      <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
        <image src="{{userInfo.avatar}}"></image>
      </button>
    </view>
    <view class='no-login' wx:else>
      <button class="avatar-wrapper" bindtap="navigateTo" data-url="/pages/auth/login/login">
        请登录
      </button>
    </view>
    <view class='userinfo-name' wx:if="{{hasLogin}}">
      <input type="nickname" class="nickname-input" placeholder="{{userInfo.nickname || userInfo.username || '请输入昵称'}}" bindinput="onInputNickname" bindblur="onSaveNickname" />
    </view>
    <view class='userinfo-name' wx:else></view>
  </view>
  <!-- end 用户信息 -->
  <view class='list-cont'>
    <!-- 订单状态 -->
    <view class='total-order'>
      <view class='order-tit'>
        <text style='font-weight:bold'>我的订单</text>
        <view class='checkmore' bindtap='navigateToOrderList' data-sts="0">
          <text>查看全部</text>
          <text class='arrowhead'></text>
        </view>
      </view>
      <view class='procedure'>
        <view class='items' bindtap='navigateTo' data-url="/pages/ucenter/order/order?type=0">
          <image src='/static/images/ticon/toPay.png'></image>
          <text>待支付</text>
          <text class='num-badge' wx:if="{{orderAmount.unPay>0}}">{{orderAmount.unPay}}</text>
        </view>
        <view class='items' bindtap='navigateTo' data-url="/pages/ucenter/order/order?type=1">
          <image src='/static/images/ticon/toDelivery.png'></image>
          <text>待发货</text>
          <text class='num-badge' wx:if="{{orderAmount.payed>0}}">{{orderAmount.payed}}</text>
        </view>
        <view class='items' bindtap='navigateTo' data-url="/pages/ucenter/order/order?type=2">
          <image src='/static/images/ticon/toTake.png'></image>
          <text>待收货</text>
          <text class='num-badge' wx:if="{{orderAmount.consignment>0}}">
            {{orderAmount.consignment}}
          </text>
        </view>
        <view class='items' bindtap='navigateTo' data-url="/pages/ucenter/order/order?type=3">
          <image src='/static/images/ticon/toComment.png'></image>
          <text>已完成</text>
        </view>
      </view>
    </view>
    <!-- end 订单状态 -->
    <view class="prod-col">
      <!--<view class="col-item" bindtap='navigateTo' data-url="/pages/ucenter/collect/collect">
        <view class="num">{{collectionCount || 0}}</view>
        <view class="tit">我的收藏</view>
      </view>-->
      <view class="col-item" bindtap='navigateTo' data-url="/pages/ucenter/recharge/recharge">
        <view class="num">¥{{userBalance || '0.00'}}</view>
        <view class="tit">账户余额</view>
      </view>
      <view class="col-item" bindtap='navigateTo' data-url="/pages/ucenter/points/points">
        <view class="num">{{userPoints || 0}}</view>
        <view class="tit">我的积分</view>
      </view>
      <view class="col-item" bindtap='navigateTo' data-url="/pages/ucenter/coupon/coupon">
        <view class="num">{{couponCount || 0}}</view>
        <view class="tit">我的优惠券</view>
      </view>
      <!-- <view class="col-item" bindtap='navigateTo' data-url="/pages/ucenter/footprint/footprint">
        <view class="num">{{footprintCount || 0}}</view>
        <view class="tit">我的足迹</view>
      </view> -->
      <!-- 团队管理入口 - 管理员和区域总监可见 -->
      <view class="col-item" bindtap='navigateTo' data-url="/pages/ucenter/team/team" wx:if="{{hasLogin && (userInfo.userLevelId == 1 || userInfo.userLevelId == 2)}}">
        <view class="num" wx:if="{{userInfo.userLevelId == 1}}">{{teamStats.totalDirectors || 0}}</view>
        <view class="num" wx:elif="{{userInfo.userLevelId == 2}}">{{teamStats.totalUsers || 0}}</view>
        <view class="tit">团队管理</view>
      </view>
    </view>
    <!-- 苹果风格菜单 -->
    <view class='my-menu ios-style'>
      <!-- 账户相关 -->
      <view class='menu-section' wx:if="{{hasLogin}}">

        <view class='menu-card'>
          <view class='section-title'>账户管理</view>
          <view class='menu-item' bindtap='navigateTo' data-url="/pages/ucenter/coupon/coupon">
            <view class="item-content">
              <view class="icon-wrapper coupon-icon">
                <image src='/static/images/ticon/myCoupon.png'></image>
              </view>
              <view class="item-info">
                <text class="item-title">我的优惠券</text>
                <text class="item-subtitle">查看可用优惠券</text>
              </view>
            </view>
            <view class='arrow-icon'>
              <text class="arrow">›</text>
            </view>
          </view>
          <view class='menu-item' bindtap='navigateTo' data-url="/pages/ucenter/recharge/recharge">
            <view class="item-content">
              <view class="icon-wrapper recharge-icon">
                <image src='/static/images/ticon/getCoupon.png'></image>
              </view>
              <view class="item-info">
                <text class="item-title">账户充值</text>
                <text class="item-subtitle">余额：<text class="highlight">¥{{userBalance || '0.00'}}</text></text>
              </view>
            </view>
            <view class='arrow-icon'>
              <text class="arrow">›</text>
            </view>
          </view>
          <view class='menu-item' bindtap='navigateTo' data-url="/pages/ucenter/points/points">
            <view class="item-content">
              <view class="icon-wrapper points-icon">
                <image src='/static/images/ticon/myCoupon.png'></image>
              </view>
              <view class="item-info">
                <text class="item-title">我的积分</text>
                <text class="item-subtitle">当前积分：<text class="highlight">{{userPoints || 0}}</text></text>
              </view>
            </view>
            <view class='arrow-icon'>
              <text class="arrow">›</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 推广相关 -->
      <view class='menu-section' wx:if="{{hasLogin}}">
        <view class='menu-card'>
          <view class='section-title'>推广中心</view>
          <view class='menu-item' bindtap="showPromotionQrCode">
            <view class="item-content">
              <view class="icon-wrapper promotion-icon">
                <image src='/static/images/ticon/promotion.png'></image>
              </view>
              <view class="item-info">
                <text class="item-title">推广二维码</text>
                <text class="item-subtitle">分享赚取佣金</text>
              </view>
            </view>
            <view class='arrow-icon'>
              <text class="arrow">›</text>
            </view>
          </view>
          <view class='menu-item' bindtap="goToPromotionDetail">
            <view class="item-content">
              <view class="icon-wrapper detail-icon">
                <image src='/static/images/svg/推广明细.svg'></image>
              </view>
              <view class="item-info">
                <text class="item-title">推广明细</text>
                <text class="item-subtitle">查看推广记录</text>
              </view>
            </view>
            <view class='arrow-icon'>
              <text class="arrow">›</text>
            </view>
          </view>
          <view class='menu-item' bindtap="goToEarnings">
            <view class="item-content">
              <view class="icon-wrapper earnings-icon">
                <image src='/static/images/svg/我的收益.svg'></image>
              </view>
              <view class="item-info">
                <text class="item-title">我的收益</text>
                <text class="item-subtitle">查看收益详情</text>
              </view>
            </view>
            <view class='arrow-icon'>
              <text class="arrow">›</text>
            </view>
          </view>
          <!-- <view class='menu-item' bindtap="goToPromotionPoster">
            <view class="item-content">
              <view class="icon-wrapper poster-icon">
                <image src='/static/images/ticon/myCoupon.png'></image>
              </view>
              <view class="item-info">
                <text class="item-title">推广海报</text>
                <text class="item-subtitle">生成专属推广海报</text>
              </view>
            </view>
            <view class='arrow-icon'>
              <text class="arrow">›</text>
            </view>
          </view>  -->

        </view>
      </view>
      <!-- 服务相关 -->
      <view class='menu-section'>
        <view class='menu-card'>
          <view class='section-title'>服务中心</view>
          <!-- 管理员中心 - 只有管理员可见 -->
          <!-- <view class='menu-item' bindtap='navigateTo' data-url="/pages/ucenter/admin/admin" wx:if="{{hasLogin && userInfo.userLevelId == 1}}">
            <view class="item-content">
              <view class="icon-wrapper admin-icon">
                <image src='/static/images/ticon/admin.png'></image>
              </view>
              <view class="item-info">
                <text class="item-title">管理员中心</text>
                <text class="item-subtitle">订单管理、用户管理</text>
              </view>
            </view>
            <view class='arrow-icon'>
              <text class="arrow">›</text>
            </view>
          </view> -->
          <view class='menu-item' bindtap="showContactModal">
            <view class="item-content">
              <view class="icon-wrapper service-icon">
                <image src='/static/images/ticon/getCoupon.png'></image>
              </view>
              <view class="item-info">
                <text class="item-title">在线客服</text>
                <text class="item-subtitle">7×24小时服务</text>
              </view>
            </view>
            <view class='arrow-icon'>
              <text class="arrow">›</text>
            </view>
          </view>
          <view class='menu-item' bindtap="callComplaintPhone">
            <view class="item-content">
              <view class="icon-wrapper complaint-icon">
                <image src='/static/images/ticon/getCoupon.png'></image>
              </view>
              <view class="item-info">
                <text class="item-title">投诉电话</text>
                <text class="item-subtitle">15332415685</text>
              </view>
            </view>
            <view class='arrow-icon'>
              <text class="arrow">›</text>
            </view>
          </view>
          <view class='menu-item' bindtap='navigateTo' data-url="/pages/shopping/address/address">
            <view class="item-content">
              <view class="icon-wrapper address-icon">
                <image src='/static/images/ticon/myAddr.png'></image>
              </view>
              <view class="item-info">
                <text class="item-title">收货地址</text>
                <text class="item-subtitle">管理收货地址</text>
              </view>
            </view>
            <view class='arrow-icon'>
              <text class="arrow">›</text>
            </view>
          </view>
          <view class='menu-item' bindtap='clearStorage'>
            <view class="item-content">
              <view class="icon-wrapper cache-icon">
                <image src='/static/images/ticon/myCoupon.png'></image>
              </view>
              <view class="item-info">
                <text class="item-title">清除缓存</text>
                <text class="item-subtitle">释放存储空间</text>
              </view>
            </view>
            <view class='arrow-icon'>
              <text class="arrow">›</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- end 列表项 -->
  </view>
  <view class="logout-btn" bindtap="logout" wx:if="{{hasLogin}}">退出登录</view>
  <view class="logout-btn" wx:if="{{hasSub && hasLogin}}" bindtap="requestSubscribeMsg">订阅通知</view>
  <!-- 底部提示 -->
  <view class="bottom-tip">已经到底了</view>
  <!-- 手机绑定弹窗 -->
  <view class="bind-phone-modal" wx:if="{{showBindPhoneModal}}">
    <view class="modal-mask" bindtap="closeBindPhoneModal"></view>
    <view class="modal-dialog">
      <view class="modal-header">
        <text class="modal-icon-text">🔗</text>
      </view>
      <view class="modal-title">需要获取您的手机号</view>
      <button class="modal-btn-primary" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">
        授权获取
      </button>
      <view class="modal-btn-secondary" bindtap="closeBindPhoneModal">暂不授权</view>
    </view>
  </view>
  <!-- 推广二维码弹窗 -->
  <view class="promotion-qr-modal" wx:if="{{showPromotionQrModal}}">
    <!-- 浮动装饰元素 -->
    <view class="floating-decorations">
      <view class="floating-item floating-item-1">✨</view>
      <view class="floating-item floating-item-2">🎉</view>
      <view class="floating-item floating-item-3">💫</view>
      <view class="floating-item floating-item-4">⭐</view>
      <view class="floating-item floating-item-5">🌟</view>
      <view class="floating-item floating-item-6">💎</view>
    </view>
    <view class="modal-mask" bindtap="closePromotionQrModal"></view>
    <view class="modal-dialog qr-dialog">
      <view class="modal-header">
        <text class="modal-icon-text">📱</text>
        <view class="header-sparkles">
          <view class="sparkle sparkle-1"></view>
          <view class="sparkle sparkle-2"></view>
          <view class="sparkle sparkle-3"></view>
        </view>
      </view>
      <view class="modal-title">
        <text class="title-text">我的推广二维码</text>
        <view class="title-underline"></view>
      </view>
      <view class="qr-content">
        <view class="qr-code-container" wx:if="{{promotionQrCode.qrCodeUrl}}">
          <image class="qr-code-image" src="{{promotionQrCode.qrCodeUrl}}" mode="aspectFit"></image>
        </view>
        <view class="qr-loading" wx:else>
          <text>生成中...</text>
        </view>
        <view class="qr-desc">
          <text>分享此二维码，好友扫码进入即可获得推广奖励</text>
        </view>
        <view class="qr-user-info">
          <text>推广码：{{promotionQrCode.scene}}</text>
        </view>
      </view>
      <view class="qr-actions">
        <view class="qr-btn qr-btn-save" bindtap="saveQrCodeToAlbum">
          <text class="btn-icon">📱</text>
          <text class="btn-text">保存到相册</text>
        </view>
        <view class="qr-btn qr-btn-share" bindtap="shareQrCode">
          <text class="btn-icon">📤</text>
          <text class="btn-text">分享给好友</text>
        </view>
      </view>
      <view class="modal-btn-secondary" bindtap="closePromotionQrModal">关闭</view>
    </view>
  </view>
  <!-- 联系客服弹窗 -->
  <view class="contact-modal" wx:if="{{showContactModal}}">
    <view class="modal-mask" bindtap="closeContactModal"></view>
    <view class="modal-dialog contact-dialog">
      <view class="modal-header">
        <text class="modal-icon-text">📞</text>
      </view>
      <view class="modal-title">联系客服</view>
      <view class="contact-options">
        <view class="contact-option" bindtap="callPhone">
          <view class="option-icon">📱</view>
          <view class="option-content">
            <view class="option-title">咨询电话</view>
            <view class="option-desc">{{customerService.phone}}</view>
            <view class="option-desc" style="font-size: 24rpx; color: #999; margin-top: 4rpx;">
              {{customerService.workTime}}
            </view>
          </view>
        </view>
        <view class="contact-option" bindtap="copyWechat">
          <view class="option-icon">💬</view>
          <view class="option-content">
            <view class="option-title">微信客服</view>
            <view class="option-desc">客服微信：{{customerService.wechat}}</view>
            <view class="option-desc" style="font-size: 24rpx; color: #999; margin-top: 4rpx;">
              点击复制微信号
            </view>
          </view>
        </view>
      </view>
      <view class="modal-btn-secondary" bindtap="closeContactModal">取消</view>
    </view>
  </view>
</view>