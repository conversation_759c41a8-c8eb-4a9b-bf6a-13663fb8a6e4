# 商品页面登录验证修改说明

## 修改目标
实现商品页面在未登录状态下允许查看商品详情，但在进行购物操作（加入购物车、立即购买、收藏）时需要登录验证。

## 修改内容

### 1. 后端配置修改

**文件**: `server/src/main/resources/weshop-wjhx.properties`

**修改内容**:
- 添加了阶梯推广商品相关API的登录验证排除路径：
  ```properties
  weshop-wjhx.login-interceptor-exclude-path[16]=/app/tiered-promotion/goods/**
  ```

**说明**: 
- 商品详情相关的API (`/wechat/goods/**`) 已经在排除列表中，无需登录即可访问
- 购物车相关API (`/wechat/cart/**`) 仍需要登录验证
- 阶梯推广商品查询API现在也无需登录即可访问

### 2. 前端商品页面修改

**文件**: `app/wjhx/pages/goods/goods.js`

#### 2.1 加入购物车功能 (`addToCart` 方法)
**修改位置**: 第581-622行

**修改内容**:
- 在执行加入购物车操作前检查用户登录状态
- 未登录时显示登录提示弹窗，引导用户去登录页面
- 登录后才能继续执行加入购物车操作

#### 2.2 收藏功能 (`addCannelCollect` 方法)
**修改位置**: 第550-594行

**修改内容**:
- 在执行收藏/取消收藏操作前检查用户登录状态
- 未登录时显示登录提示弹窗，引导用户去登录页面
- 登录后才能继续执行收藏操作

#### 2.3 购物车数量获取 (`getCartList` 方法)
**修改位置**: 第207-223行

**修改内容**:
- 未登录时将购物车数量设置为0
- 登录时正常获取购物车数量
- 避免未登录时调用需要登录验证的API

#### 2.4 页面显示逻辑 (`onShow` 方法)
**修改位置**: 第426-438行

**修改内容**:
- 页面显示时重新获取购物车数量
- 确保在用户登录状态变化时正确更新购物车数量显示

### 3. 购物车服务修改

**文件**: `app/wjhx/services/cart.js`

#### 3.1 加入购物车服务 (`addToCart` 函数)
**修改位置**: 第22-71行

**修改内容**:
- 在函数开始时检查用户登录状态
- 未登录时显示登录提示弹窗并返回Promise.reject
- 确保所有通过服务调用的加入购物车操作都需要登录验证

## 功能验证

### 未登录状态下的行为
1. ✅ 可以正常浏览商品详情页面
2. ✅ 可以查看商品图片、价格、规格等信息
3. ✅ 可以查看商品评价和相关推荐
4. ✅ 购物车图标显示数量为0
5. ✅ 点击"加入购物车"按钮时提示登录
6. ✅ 点击"收藏"按钮时提示登录
7. ✅ 点击"立即购买"按钮时提示登录（原有逻辑）

### 登录状态下的行为
1. ✅ 所有未登录状态的功能继续可用
2. ✅ 可以正常加入购物车
3. ✅ 可以正常收藏/取消收藏商品
4. ✅ 可以正常立即购买
5. ✅ 购物车图标显示正确的商品数量

## 技术实现细节

### 登录状态检查
使用 `wx.getStorageSync('userInfo')` 检查用户登录状态，这与系统其他地方的登录检查保持一致。

### 用户体验优化
1. 提供友好的登录提示弹窗
2. 提供"去登录"和"稍后"两个选项
3. 点击"去登录"直接跳转到登录页面
4. 保持原有的操作流程和界面不变

### 错误处理
1. 在服务层面进行登录验证，确保安全性
2. 提供适当的错误回调处理
3. 返回Promise.reject以便上层代码处理

## 注意事项

1. **安全性**: 后端API仍然有登录验证，前端检查主要是为了用户体验
2. **一致性**: 所有购物相关操作都需要登录，保持行为一致
3. **兼容性**: 修改不影响已登录用户的正常使用
4. **扩展性**: 如果将来需要调整登录验证策略，只需修改配置文件

## 测试建议

1. 测试未登录状态下的所有功能点
2. 测试登录后的所有功能点
3. 测试登录状态变化时的页面行为
4. 测试不同商品类型（单规格、多规格）的行为
5. 测试推荐商品区域的加入购物车功能
