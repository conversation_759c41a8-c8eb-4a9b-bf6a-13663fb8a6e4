package com.logic.code.entity.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 商品浏览记录实体类
 * <AUTHOR>
 */
@Data
@TableName("goods_view_log")
public class GoodsViewLog {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 商品ID
     */
    private Integer goodsId;
    
    /**
     * 浏览时间
     */
    private Date viewTime;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 来源类型：1-小程序，2-H5，3-APP
     */
    private Integer sourceType;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 来源页面
     */
    private String referer;
    
    /**
     * 浏览时长（秒）
     */
    private Integer viewDuration;
    
    /**
     * 是否首次浏览：0-否，1-是
     */
    private Integer isFirstView;
    
    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 更新时间
     */
    private Date updatedAt;
}