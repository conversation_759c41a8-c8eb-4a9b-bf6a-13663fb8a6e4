package com.logic.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logic.code.entity.goods.GoodsViewLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;

/**
 * 商品浏览记录Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface GoodsViewLogMapper extends BaseMapper<GoodsViewLog> {

    /**
     * 获取指定时间范围内的商品浏览量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 浏览量
     */
    @Select("SELECT COUNT(*) FROM goods_view_log WHERE view_time BETWEEN #{startTime} AND #{endTime}")
    Long getViewCountBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}