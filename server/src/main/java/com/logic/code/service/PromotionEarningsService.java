package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.logic.code.entity.PromotionEarnings;
import com.logic.code.entity.User;
import com.logic.code.entity.WithdrawRecord;
import com.logic.code.entity.order.OrderGoods;
import com.logic.code.entity.order.Order;
import com.logic.code.mapper.PromotionEarningsMapper;
import com.logic.code.mapper.UserMapper;
import com.logic.code.mapper.WithdrawRecordMapper;
import com.logic.code.mapper.OrderGoodsMapper;
import com.logic.code.mapper.OrderMapper;
import jakarta.annotation.Resource;
import logic.orm.utils.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 推广收益服务
 */
@Service
public class PromotionEarningsService extends ServiceImpl<PromotionEarningsMapper, PromotionEarnings> {

    @Autowired
    private WithdrawRecordMapper withdrawRecordMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private OrderGoodsMapper orderGoodsMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Resource
    private PromotionEarningsMapper promotionEarningsMapper;

    /**
     * 获取用户收益统计
     * @param userId 用户ID
     * @return 收益统计信息
     */
    public Map<String, Object> getEarningsStats(Integer userId) {
        Map<String, Object> result = new HashMap<>();

        // 获取总收益（已确认）
        BigDecimal totalEarnings = getTotalEarnings(userId);
        result.put("totalEarnings", totalEarnings.toString());

        // 获取今日收益
        BigDecimal todayEarnings = getTodayEarnings(userId);
        result.put("todayEarnings", todayEarnings.toString());

        // 获取上月收益
        BigDecimal lastMonthEarnings = getLastMonthEarnings(userId);
        result.put("lastMonthEarnings", lastMonthEarnings.toString());

        // 获取待确认收益
        BigDecimal pendingEarnings = getPendingEarnings(userId);
        result.put("pendingEarnings", pendingEarnings.toString());

        // 获取今日预估收入
        BigDecimal todayEstimated = getTodayEstimatedEarnings(userId);
        result.put("todayEstimated", todayEstimated.toString());

        // 获取本月预估收入
        BigDecimal monthEstimated = getMonthEstimatedEarnings(userId);
        result.put("monthEstimated", monthEstimated.toString());

        // 获取今日订单数
        int todayOrders = getTodayOrderCount(userId);
        result.put("todayOrders", todayOrders);

        // 获取本月订单数
        int monthOrders = getMonthOrderCount(userId);
        result.put("monthOrders", monthOrders);

        // 获取可提现金额
        BigDecimal withdrawableAmount = getWithdrawableAmount(userId);
        result.put("withdrawableAmount", withdrawableAmount.toString());

        // 获取最近收益列表（包含完整订单详情）
        List<Map<String, Object>> recentEarnings = getRecentEarnings(userId, 10);
        result.put("earningsList", recentEarnings);

        return result;
    }

    /**
     * 获取总收益（已确认）- 使用自定义Mapper方法
     */
    public BigDecimal getTotalEarnings(Integer userId) {
        return promotionEarningsMapper.selectTotalEarnings(userId);
    }

    /**
     * 获取今日收益（修复版本）
     * 修复：统一使用 create_time 进行时间判断，使用自定义Mapper方法
     */
    private BigDecimal getTodayEarnings(Integer userId) {
        LocalDate today = LocalDate.now();
        Date startOfDay = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endOfDay = Date.from(today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        return promotionEarningsMapper.selectEarningsByStatus(userId, "confirmed", startOfDay, endOfDay);
    }

    /**
     * 获取上月收益（修复版本）
     * 修复：统一使用 order_create_time 进行时间判断，使用自定义Mapper方法
     */
    public BigDecimal getLastMonthEarnings(Integer userId) {
        LocalDate lastMonth = LocalDate.now().minusMonths(1);
        Date startOfMonth = Date.from(lastMonth.withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endOfMonth = Date.from(lastMonth.withDayOfMonth(lastMonth.lengthOfMonth()).plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        return promotionEarningsMapper.selectEarningsByStatus(userId, "confirmed", startOfMonth, endOfMonth);
    }

    /**
     * 获取待确认收益（修复聚合查询问题）
     */
    private BigDecimal getPendingEarnings(Integer userId) {
        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.eq("promoter_id", userId)
               .eq("status", "pending");

        // 修复：直接查询列表并计算总和，避免聚合查询映射问题
        return this.list(wrapper).stream()
                .map(PromotionEarnings::getCommissionAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取今日预估收入（修复聚合查询问题）
     * 修复：使用 order_create_time 而不是 create_time，确保按订单实际创建日期统计
     */
    public BigDecimal getTodayEstimatedEarnings(Integer userId) {
        LocalDate today = LocalDate.now();
        Date startOfDay = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endOfDay = Date.from(today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.eq("promoter_id", userId)
               .in("status", Arrays.asList("pending", "confirmed"))
               .ge("order_create_time", startOfDay)
               .lt("order_create_time", endOfDay);

        // 修复：直接查询列表并计算总和，避免聚合查询映射问题
        return this.list(wrapper).stream()
                .map(PromotionEarnings::getCommissionAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取本月预估收入（修复聚合查询问题）
     * 修复：使用 order_create_time 而不是 create_time，确保按订单实际创建日期统计
     */
    public BigDecimal getMonthEstimatedEarnings(Integer userId) {
        LocalDate thisMonth = LocalDate.now();
        Date startOfMonth = Date.from(thisMonth.withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endOfMonth = Date.from(thisMonth.withDayOfMonth(thisMonth.lengthOfMonth()).plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.eq("promoter_id", userId)
               .in("status", Arrays.asList("pending", "confirmed"))
               .ge("order_create_time", startOfMonth)
               .lt("order_create_time", endOfMonth);

        // 修复：直接查询列表并计算总和，避免聚合查询映射问题
        return this.list(wrapper).stream()
                .map(PromotionEarnings::getCommissionAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 批量获取今日预估收入
     * @param userIds 用户ID列表
     * @return 用户ID到今日预估收入的映射
     */
    public Map<Integer, BigDecimal> getBatchTodayEstimatedEarnings(List<Integer> userIds) {
        Map<Integer, BigDecimal> resultMap = new HashMap<>();

        if (userIds == null || userIds.isEmpty()) {
            return resultMap;
        }

        LocalDate today = LocalDate.now();
        Date startOfDay = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endOfDay = Date.from(today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.in("promoter_id", userIds)
               .in("status", Arrays.asList("pending", "confirmed"))
               .ge("order_create_time", startOfDay)
               .lt("order_create_time", endOfDay)
               .select("promoter_id", "IFNULL(SUM(commission_amount), 0) as total_commission")
               .groupBy("promoter_id");

        try {
            List<Map<String, Object>> results = this.baseMapper.selectMaps(wrapper);
            for (Map<String, Object> result : results) {
                Integer promoterId = (Integer) result.get("promoter_id");
                BigDecimal totalCommission = new BigDecimal(result.get("total_commission").toString());
                resultMap.put(promoterId, totalCommission);
            }
        } catch (Exception e) {
            // 如果聚合查询失败，回退到逐个查询
            for (Integer userId : userIds) {
                resultMap.put(userId, getTodayEstimatedEarnings(userId));
            }
        }

        // 为没有收益的用户设置默认值
        for (Integer userId : userIds) {
            resultMap.putIfAbsent(userId, BigDecimal.ZERO);
        }

        return resultMap;
    }

    /**
     * 批量获取本月预估收入
     * @param userIds 用户ID列表
     * @return 用户ID到本月预估收入的映射
     */
    public Map<Integer, BigDecimal> getBatchMonthEstimatedEarnings(List<Integer> userIds) {
        Map<Integer, BigDecimal> resultMap = new HashMap<>();

        if (userIds == null || userIds.isEmpty()) {
            return resultMap;
        }

        LocalDate thisMonth = LocalDate.now();
        Date startOfMonth = Date.from(thisMonth.withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endOfMonth = Date.from(thisMonth.withDayOfMonth(thisMonth.lengthOfMonth()).plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.in("promoter_id", userIds)
               .in("status", Arrays.asList("pending", "confirmed"))
               .ge("order_create_time", startOfMonth)
               .lt("order_create_time", endOfMonth)
               .select("promoter_id", "IFNULL(SUM(commission_amount), 0) as total_commission")
               .groupBy("promoter_id");

        try {
            List<Map<String, Object>> results = this.baseMapper.selectMaps(wrapper);
            for (Map<String, Object> result : results) {
                Integer promoterId = (Integer) result.get("promoter_id");
                BigDecimal totalCommission = new BigDecimal(result.get("total_commission").toString());
                resultMap.put(promoterId, totalCommission);
            }
        } catch (Exception e) {
            // 如果聚合查询失败，回退到逐个查询
            for (Integer userId : userIds) {
                resultMap.put(userId, getMonthEstimatedEarnings(userId));
            }
        }

        // 为没有收益的用户设置默认值
        for (Integer userId : userIds) {
            resultMap.putIfAbsent(userId, BigDecimal.ZERO);
        }

        return resultMap;
    }

    /**
     * 获取今日订单数
     * 修复：使用 order_create_time 而不是 create_time，确保按订单实际创建日期统计
     */
    private int getTodayOrderCount(Integer userId) {
        LocalDate today = LocalDate.now();
        Date startOfDay = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endOfDay = Date.from(today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.eq("promoter_id", userId)
               .ge("order_create_time", startOfDay)
               .lt("order_create_time", endOfDay);

        return (int) this.count(wrapper);
    }

    /**
     * 获取本月订单数
     * 修复：使用 order_create_time 而不是 create_time，确保按订单实际创建日期统计
     */
    private int getMonthOrderCount(Integer userId) {
        LocalDate thisMonth = LocalDate.now();
        Date startOfMonth = Date.from(thisMonth.withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endOfMonth = Date.from(thisMonth.withDayOfMonth(thisMonth.lengthOfMonth()).plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.eq("promoter_id", userId)
               .ge("order_create_time", startOfMonth)
               .lt("order_create_time", endOfMonth);

        return (int) this.count(wrapper);
    }

    /**
     * 获取可提现金额（优化版本）
     * 计算逻辑：所有已确认的收益 - 已提现的金额 - 已标记为已提现的收益
     */
    private BigDecimal getWithdrawableAmount(Integer userId) {
        // 获取所有已确认但未标记为已提现的收益
        QueryWrapper<PromotionEarnings> confirmedWrapper = new QueryWrapper<>();
        confirmedWrapper.eq("promoter_id", userId)
                       .eq("status", "confirmed")
                       .eq("is_withdrawn", 0); // 未提现的收益

        BigDecimal confirmedEarnings = this.list(confirmedWrapper).stream()
                .map(PromotionEarnings::getCommissionAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return confirmedEarnings.max(BigDecimal.ZERO);
    }

    /**
     * 获取可提现金额（新增公共方法）
     * @param userId 用户ID
     * @return 可提现金额
     */
    public BigDecimal getWithdrawableAmountPublic(Integer userId) {
        return getWithdrawableAmount(userId);
    }

    /**
     * 标记收益为已提现状态
     * @param userId 用户ID
     * @param withdrawAmount 提现金额
     * @return 是否成功
     */
    public boolean markEarningsAsWithdrawn(Integer userId, BigDecimal withdrawAmount) {
        try {
            // 获取可提现的收益记录，按时间顺序排序（先进先出）
            QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
            wrapper.eq("promoter_id", userId)
                   .eq("status", "confirmed")
                   .eq("is_withdrawn", 0)
                   .orderByAsc("effective_time");

            List<PromotionEarnings> availableEarnings = this.list(wrapper);

            BigDecimal remainingAmount = withdrawAmount;
            List<PromotionEarnings> toUpdate = new ArrayList<>();

            // 按顺序标记收益为已提现，直到达到提现金额
            for (PromotionEarnings earning : availableEarnings) {
                if (remainingAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }

                BigDecimal earningAmount = earning.getCommissionAmount();
                if (remainingAmount.compareTo(earningAmount) >= 0) {
                    // 完全提现这笔收益
                    earning.setIsWithdrawn(1);
                    earning.setWithdrawTime(new Date());
                    earning.setUpdateTime(new Date());
                    toUpdate.add(earning);
                    remainingAmount = remainingAmount.subtract(earningAmount);
                } else {
                    // 部分提现（这种情况下需要拆分记录，暂时简化处理）
                    earning.setIsWithdrawn(1);
                    earning.setWithdrawTime(new Date());
                    earning.setUpdateTime(new Date());
                    toUpdate.add(earning);
                    remainingAmount = BigDecimal.ZERO;
                }
            }

            // 批量更新
            if (!toUpdate.isEmpty()) {
                this.updateBatchById(toUpdate);
            }

            return remainingAmount.compareTo(BigDecimal.ZERO) == 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取最近收益列表（包含完整订单详情）
     */
    private List<Map<String, Object>> getRecentEarnings(Integer userId, int limit) {
        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.eq("promoter_id", userId)
               .orderByDesc("create_time")
               .last("LIMIT " + limit);

        List<PromotionEarnings> earningsList = this.list(wrapper);

        // 如果没有数据，直接返回空列表
        if (earningsList.isEmpty()) {
            return new ArrayList<>();
        }

        // 批量收集所有需要的ID，避免N+1查询问题
        Set<Integer> orderIds = earningsList.stream()
            .map(PromotionEarnings::getOrderId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        Set<Integer> userIds = earningsList.stream()
            .map(PromotionEarnings::getPromotedUserId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        // 批量查询相关数据，将N+1查询优化为3个批量查询
        Map<Integer, Order> orderMap = orderIds.isEmpty() ? new HashMap<>() :
            orderMapper.selectBatchIds(orderIds).stream()
                .collect(Collectors.toMap(Order::getId, order -> order, (o1, o2) -> o1));

        Map<Integer, User> userMap = userIds.isEmpty() ? new HashMap<>() :
            userMapper.selectBatchIds(userIds).stream()
                .collect(Collectors.toMap(User::getId, user -> user, (u1, u2) -> u1));

        // 批量查询并预处理订单商品信息
        Map<Integer, List<Map<String, Object>>> orderGoodsMap = buildOrderGoodsMap(orderIds);

        // 使用Stream API和已有的优化方法构建结果
        return earningsList.stream()
            .map(earnings -> buildOrderDetail(earnings, orderMap, userMap, orderGoodsMap))
            .collect(Collectors.toList());
    }

    /**
     * 创建收益记录
     */
    public void createEarningsRecord(Integer promoterId, Integer promotedUserId, Integer orderId,
                                   String orderNo, BigDecimal orderAmount) {
        PromotionEarnings earnings = new PromotionEarnings();
        earnings.setPromoterId(promoterId);
        earnings.setPromotedUserId(promotedUserId);
        earnings.setOrderId(orderId);
        earnings.setOrderNo(orderNo);
        earnings.setOrderAmount(orderAmount);
        earnings.setCommissionRate(new BigDecimal("10.00")); // 10%佣金
        earnings.setCommissionAmount(orderAmount.multiply(new BigDecimal("0.10")));
        earnings.setStatus("pending");
        earnings.setOrderCreateTime(new Date());
        earnings.setDescription("推广用户下单，待确认收货");
        earnings.setCreateTime(new Date());

        this.save(earnings);
    }

    /**
     * 确认收益（确认收货后调用）
     */
    public void confirmEarnings(Integer orderId) {
        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.eq("order_id", orderId)
               .eq("status", "pending");

        List<PromotionEarnings> list = this.list(wrapper);
        if(ListUtils.isNotBlank(list)){
            for (PromotionEarnings earnings : list) {
                earnings.setStatus("confirmed");
                earnings.setConfirmTime(new Date());
                earnings.setEffectiveTime(new Date());
                earnings.setDescription(earnings.getDescription()+" 用户已确认收货，佣金生效");
                earnings.setUpdateTime(new Date());
                this.updateById(earnings);
            }

        }
    }

    /**
     * 取消收益（订单取消后调用）
     */
    public void cancelEarnings(Integer orderId) {
        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.eq("order_id", orderId)
               .eq("status", "pending");

        PromotionEarnings earnings = this.getOne(wrapper);
        if (earnings != null) {
            earnings.setStatus("cancelled");
            earnings.setDescription("订单已取消，佣金取消");
            earnings.setUpdateTime(new Date());

            this.updateById(earnings);
        }
    }

    /**
     * 获取每日收益统计
     * @param userId 用户ID
     * @return 每日收益统计
     */
    public Map<String, Object> getDailyEarningsStats(Integer userId) {
        Map<String, Object> result = new HashMap<>();

        // 获取当月每日数据
        LocalDate now = LocalDate.now();
        LocalDate startOfMonth = now.withDayOfMonth(1);
        LocalDate endOfMonth = now.withDayOfMonth(now.lengthOfMonth());

        // 转换为Date类型用于数据库查询
        Date startDate = Date.from(startOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date queryEndDate = Date.from(endOfMonth.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 从数据库查询实际的每日收益数据
        List<Map<String, Object>> dailyEarningsFromDB = promotionEarningsMapper.selectDailyEarningsStats(
                userId, startDate, queryEndDate);

        // 创建日期到数据的映射，便于快速查找
        Map<String, Map<String, Object>> dateToDataMap = dailyEarningsFromDB.stream()
                .collect(Collectors.toMap(
                        data -> data.get("date").toString(),
                        data -> data,
                        (existing, replacement) -> existing
                ));

        List<Map<String, Object>> dailyList = new ArrayList<>();
        BigDecimal monthTotalIncome = BigDecimal.ZERO;
        int monthTotalOrders = 0;

        // 遍历当月每一天，但只到今日为止（不显示未来日期）
        LocalDate endDate = now.isBefore(endOfMonth) ? now : endOfMonth;

        for (LocalDate date = startOfMonth; !date.isAfter(endDate); date = date.plusDays(1)) {
            Map<String, Object> dayData = new HashMap<>();
            dayData.put("date", date.toString());

            // 从数据库查询结果中获取该日期的数据
            Map<String, Object> dbData = dateToDataMap.get(date.toString());

            BigDecimal dayIncome;
            int dayOrders;

            if (dbData != null) {
                // 有数据的日期，使用实际数据
                dayIncome = (BigDecimal) dbData.get("estimatedIncome");
                dayOrders = ((Long) dbData.get("orderCount")).intValue();
            } else {
                // 没有数据的日期，使用0
                dayIncome = BigDecimal.ZERO;
                dayOrders = 0;
            }

            dayData.put("estimatedIncome", dayIncome.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            dayData.put("orderCount", dayOrders);

            monthTotalIncome = monthTotalIncome.add(dayIncome);
            monthTotalOrders += dayOrders;

            dailyList.add(dayData);
        }

        Map<String, Object> monthTotal = new HashMap<>();
        monthTotal.put("income", monthTotalIncome.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        monthTotal.put("orders", monthTotalOrders);

        result.put("dailyList", dailyList);
        result.put("monthTotal", monthTotal);

        return result;
    }

    /**
     * 获取月度收益统计
     * @param userId 用户ID
     * @return 月度收益统计
     */
    public Map<String, Object> getMonthlyEarningsStats(Integer userId) {
        Map<String, Object> result = new HashMap<>();

        // 获取近6个月数据
        LocalDate now = LocalDate.now();
        List<Map<String, Object>> monthlyList = new ArrayList<>();
        BigDecimal totalIncome = BigDecimal.ZERO;
        int totalOrders = 0;

        for (int i = 5; i >= 0; i--) {
            LocalDate monthDate = now.minusMonths(i);
            Map<String, Object> monthData = new HashMap<>();
            monthData.put("month", monthDate.toString().substring(0, 7)); // YYYY-MM格式

            // 查询实际的月度数据
            Map<String, Object> monthStats = getMonthEarningsData(userId, monthDate);
            BigDecimal monthIncome = (BigDecimal) monthStats.get("income");
            Integer monthOrders = (Integer) monthStats.get("orders");

            monthData.put("estimatedIncome", monthIncome.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            monthData.put("orderCount", monthOrders);

            totalIncome = totalIncome.add(monthIncome);
            totalOrders += monthOrders;

            monthlyList.add(monthData);
        }

        Map<String, Object> totalStats = new HashMap<>();
        totalStats.put("income", totalIncome.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        totalStats.put("orders", totalOrders);

        result.put("monthlyList", monthlyList);
        result.put("totalStats", totalStats);

        return result;
    }

    /**
     * 获取指定月份的收益数据（修复版本）
     * @param userId 用户ID
     * @param monthDate 月份日期
     * @return 月度收益数据
     */
    private Map<String, Object> getMonthEarningsData(Integer userId, LocalDate monthDate) {
        Map<String, Object> result = new HashMap<>();

        // 计算月份的开始和结束时间
        LocalDate startOfMonth = monthDate.withDayOfMonth(1);
        LocalDate endOfMonth = monthDate.withDayOfMonth(monthDate.lengthOfMonth());
        Date startDate = Date.from(startOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(endOfMonth.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 修复：统一使用 order_create_time 作为时间判断标准
        // 这样确保按订单实际创建日期进行统计，与其他日期相关查询保持一致

        // 查询订单数量（使用 order_create_time 保持一致性）
        QueryWrapper<PromotionEarnings> orderCountWrapper = new QueryWrapper<>();
        orderCountWrapper.eq("promoter_id", userId)
                        .in("status", Arrays.asList("pending", "confirmed"))
                        .ge("order_create_time", startDate)
                        .lt("order_create_time", endDate);

        List<PromotionEarnings> orderList = this.list(orderCountWrapper);

        // 计算总收入（已确认 + 待确认）
        BigDecimal totalIncome = orderList.stream()
                .map(PromotionEarnings::getCommissionAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 统计不重复的订单ID数量
        long distinctOrderCount = orderList.stream()
                                          .map(PromotionEarnings::getOrderId)
                                          .filter(Objects::nonNull)  // 修复：过滤空值
                                          .distinct()
                                          .count();
        Integer orderCount = (int) distinctOrderCount;

        result.put("income", totalIncome);
        result.put("orders", orderCount);

        return result;
    }

    /**
     * 获取每日订单明细（优化版本 - 解决N+1查询问题）
     * @param userId 用户ID
     * @param date 日期
     * @return 每日订单明细
     */
    public Map<String, Object> getDayOrderDetail(Integer userId, String date) {
        Map<String, Object> result = new HashMap<>();

        // 参数验证和日期解析
        if (userId == null || date == null || date.trim().isEmpty()) {
            throw new IllegalArgumentException("用户ID和日期参数不能为空");
        }

        LocalDate targetDate;
        try {
            targetDate = LocalDate.parse(date);
        } catch (Exception e) {
            throw new IllegalArgumentException("日期格式错误，请使用 YYYY-MM-DD 格式");
        }

        // 计算日期的开始和结束时间
        Date startDate = Date.from(targetDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(targetDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 优化1: 查询指定日期的推广收益记录，使用 order_create_time 确保按订单实际创建日期查询
        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.eq("promoter_id", userId)
               .in("status", Arrays.asList("pending", "confirmed"))
               .ge("order_create_time", startDate)
               .lt("order_create_time", endDate)
               .orderByDesc("order_create_time");

        List<PromotionEarnings> earningsList = this.list(wrapper);

        // 如果没有数据，直接返回空结果
        if (earningsList.isEmpty()) {
            Map<String, Object> emptyStats = new HashMap<>();
            emptyStats.put("estimatedIncome", "0.00");
            emptyStats.put("orderCount", 0);
            emptyStats.put("commissionAmount", "0.00");

            result.put("orderList", new ArrayList<>());
            result.put("dayStats", emptyStats);
            return result;
        }

        // 优化2: 批量收集所有需要的ID，避免N+1查询问题
        Set<Integer> orderIds = earningsList.stream()
            .map(PromotionEarnings::getOrderId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        Set<Integer> userIds = earningsList.stream()
            .map(PromotionEarnings::getPromotedUserId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        // 优化3: 批量查询相关数据，将N+1查询优化为3个批量查询
        Map<Integer, Order> orderMap = orderIds.isEmpty() ? new HashMap<>() :
            orderMapper.selectBatchIds(orderIds).stream()
                .collect(Collectors.toMap(Order::getId, order -> order, (o1, o2) -> o1));

        Map<Integer, User> userMap = userIds.isEmpty() ? new HashMap<>() :
            userMapper.selectBatchIds(userIds).stream()
                .collect(Collectors.toMap(User::getId, user -> user, (u1, u2) -> u1));

        // 优化4: 批量查询并预处理订单商品信息
        Map<Integer, List<Map<String, Object>>> orderGoodsMap = buildOrderGoodsMap(orderIds);

        // 优化5: 使用Stream API和已有的优化方法构建结果
        List<Map<String, Object>> orderList = earningsList.stream()
            .map(earnings -> buildOrderDetail(earnings, orderMap, userMap, orderGoodsMap))
            .collect(Collectors.toList());

        // 计算统计数据
        BigDecimal dayIncome = earningsList.stream()
            .map(PromotionEarnings::getOrderAmount)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal dayCommission = earningsList.stream()
            .map(PromotionEarnings::getCommissionAmount)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        Map<String, Object> dayStats = new HashMap<>();
        dayStats.put("estimatedIncome", dayIncome.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        dayStats.put("orderCount", earningsList.size());
        dayStats.put("commissionAmount", dayCommission.setScale(2, BigDecimal.ROUND_HALF_UP).toString());

        result.put("orderList", orderList);
        result.put("dayStats", dayStats);

        return result;
    }

    /**
     * 获取月度订单明细（优化版本）
     * @param userId 用户ID
     * @param month 月份
     * @return 月度订单明细
     */
    public Map<String, Object> getMonthOrderDetail(Integer userId, String month) {
        Map<String, Object> result = new HashMap<>();

        // 参数验证和日期解析
        if (userId == null || month == null || month.trim().isEmpty()) {
            throw new IllegalArgumentException("用户ID和月份参数不能为空");
        }

        LocalDate targetMonth;
        try {
            targetMonth = LocalDate.parse(month + "-01");
        } catch (Exception e) {
            throw new IllegalArgumentException("月份格式错误，请使用 YYYY-MM 格式");
        }

        // 计算月份的开始和结束时间
        Date startDate = Date.from(targetMonth.withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(targetMonth.withDayOfMonth(targetMonth.lengthOfMonth()).plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 优化1: 先获取统计数据，如果没有数据直接返回
        Map<String, Object> statsMap = promotionEarningsMapper.selectEarningsStats(
                userId, startDate, endDate);

        if (statsMap == null || ((Long) statsMap.get("orderCount")).intValue() == 0) {
            // 没有数据时直接返回空结果
            Map<String, Object> emptyStats = new HashMap<>();
            emptyStats.put("estimatedIncome", "0.00");
            emptyStats.put("orderCount", 0);
            emptyStats.put("commissionAmount", "0.00");

            result.put("orderList", new ArrayList<>());
            result.put("monthStats", emptyStats);
            return result;
        }

        // 获取统计数据
        int orderCount = ((Long) statsMap.get("orderCount")).intValue();
        BigDecimal monthCommission = (BigDecimal) statsMap.get("totalCommission");

        // 优化2: 一次性查询所有收益记录，避免分页循环
        // 修复：使用 order_create_time 进行日期过滤，确保按订单实际创建日期查询
        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.eq("promoter_id", userId)
               .in("status", Arrays.asList("pending", "confirmed"))
               .ge("order_create_time", startDate)
               .lt("order_create_time", endDate)
               .orderByDesc("order_create_time");

        List<PromotionEarnings> earningsList = this.list(wrapper);

        if (earningsList.isEmpty()) {
            Map<String, Object> emptyStats = new HashMap<>();
            emptyStats.put("estimatedIncome", "0.00");
            emptyStats.put("orderCount", 0);
            emptyStats.put("commissionAmount", "0.00");

            result.put("orderList", new ArrayList<>());
            result.put("monthStats", emptyStats);
            return result;
        }

        // 优化3: 批量收集所有需要的ID，减少重复操作
        Set<Integer> orderIds = earningsList.stream()
            .map(PromotionEarnings::getOrderId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        Set<Integer> userIds = earningsList.stream()
            .map(PromotionEarnings::getPromotedUserId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        // 优化4: 并行批量查询相关数据
        Map<Integer, Order> orderMap = orderIds.isEmpty() ? new HashMap<>() :
            orderMapper.selectBatchIds(orderIds).stream()
                .collect(Collectors.toMap(Order::getId, order -> order, (o1, o2) -> o1));

        Map<Integer, User> userMap = userIds.isEmpty() ? new HashMap<>() :
            userMapper.selectBatchIds(userIds).stream()
                .collect(Collectors.toMap(User::getId, user -> user, (u1, u2) -> u1));

        // 优化5: 批量查询并预处理订单商品信息
        Map<Integer, List<Map<String, Object>>> orderGoodsMap = buildOrderGoodsMap(orderIds);

        // 优化6: 使用Stream API构建结果，提高代码可读性和性能
        List<Map<String, Object>> orderList = earningsList.stream()
            .map(earnings -> buildOrderDetail(earnings, orderMap, userMap, orderGoodsMap))
            .collect(Collectors.toList());

        // 构建统计信息
        Map<String, Object> monthStats = new HashMap<>();
        monthStats.put("estimatedIncome", monthCommission.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        monthStats.put("orderCount", orderCount);
        monthStats.put("commissionAmount", monthCommission.setScale(2, BigDecimal.ROUND_HALF_UP).toString());

        result.put("orderList", orderList);
        result.put("monthStats", monthStats);

        return result;
    }

    /**
     * 批量构建订单商品映射（优化版本）
     */
    private Map<Integer, List<Map<String, Object>>> buildOrderGoodsMap(Set<Integer> orderIds) {
        if (orderIds.isEmpty()) {
            return new HashMap<>();
        }

        QueryWrapper<OrderGoods> goodsWrapper = new QueryWrapper<>();
        goodsWrapper.in("order_id", orderIds);
        List<OrderGoods> allOrderGoods = orderGoodsMapper.selectList(goodsWrapper);

        return allOrderGoods.stream()
            .collect(Collectors.groupingBy(
                OrderGoods::getOrderId,
                Collectors.mapping(this::convertOrderGoodsToMap, Collectors.toList())
            ));
    }

    /**
     * 转换OrderGoods为Map（优化版本）
     */
    private Map<String, Object> convertOrderGoodsToMap(OrderGoods og) {
        Map<String, Object> goods = new HashMap<>();
        goods.put("id", og.getId());
        goods.put("goodsId", og.getGoodsId());
        goods.put("goodsName", og.getGoodsName());
        goods.put("goodsSn", og.getGoodsSn());
        goods.put("productId", og.getProductId());
        goods.put("number", og.getNumber());
        goods.put("marketPrice", og.getMarketPrice() != null ?
            og.getMarketPrice().setScale(2, BigDecimal.ROUND_HALF_UP).toString() : "0.00");
        goods.put("retailPrice", og.getRetailPrice() != null ?
            og.getRetailPrice().setScale(2, BigDecimal.ROUND_HALF_UP).toString() : "0.00");
        goods.put("listPicUrl", og.getListPicUrl() != null ? og.getListPicUrl() : "");
        goods.put("goodsSpecificationIds", og.getGoodsSpecificationIds() != null ?
            og.getGoodsSpecificationIds() : "");
        goods.put("goodsSpecificationNameValue", og.getGoodsSpecificationNameValue() != null ?
            og.getGoodsSpecificationNameValue() : "");
        return goods;
    }

    /**
     * 构建单个订单详情（优化版本）
     */
    private Map<String, Object> buildOrderDetail(PromotionEarnings earnings,
                                                Map<Integer, Order> orderMap,
                                                Map<Integer, User> userMap,
                                                Map<Integer, List<Map<String, Object>>> orderGoodsMap) {
        Map<String, Object> order = new HashMap<>();

        // 基础信息
        order.put("id", earnings.getId());
        order.put("orderId", earnings.getOrderId());
        order.put("orderNo", earnings.getOrderNo());
        order.put("orderAmount", earnings.getOrderAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        order.put("commissionRate", earnings.getCommissionRate().toString());
        order.put("commissionAmount", earnings.getCommissionAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        order.put("status", earnings.getStatus());
        order.put("orderCreateTime", earnings.getOrderCreateTime());
        order.put("confirmTime", earnings.getConfirmTime());
        order.put("effectiveTime", earnings.getEffectiveTime());
        order.put("description", earnings.getDescription());

        // 订单信息（收货人信息）
        Order orderInfo = orderMap.get(earnings.getOrderId());
        if (orderInfo != null) {
            order.put("consignee", orderInfo.getConsignee() != null ? orderInfo.getConsignee() : "");
            order.put("mobile", orderInfo.getMobile() != null ? orderInfo.getMobile() : "");
            order.put("address", orderInfo.getAddress() != null ? orderInfo.getAddress() : "");
        } else {
            order.put("consignee", "");
            order.put("mobile", "");
            order.put("address", "");
        }

        // 被推广用户信息
        if (earnings.getPromotedUserId() != null) {
            User promotedUser = userMap.get(earnings.getPromotedUserId());
            order.put("userAvatar", promotedUser != null && promotedUser.getAvatar() != null ?
                promotedUser.getAvatar() : "");
        } else {
            order.put("userAvatar", "");
        }

        // 订单商品信息
        order.put("orderGoods", orderGoodsMap.getOrDefault(earnings.getOrderId(), new ArrayList<>()));

        return order;
    }


    /**
     * 获取订单商品信息
     * @param orderId 订单ID
     * @return 订单商品列表
     */
    private List<Map<String, Object>> getOrderGoodsInfo(Integer orderId) {
        List<Map<String, Object>> goodsList = new ArrayList<>();

        if (orderId == null) {
            return goodsList;
        }

        // 查询订单商品
        QueryWrapper<OrderGoods> wrapper = new QueryWrapper<>();
        wrapper.eq("order_id", orderId);
        List<OrderGoods> orderGoodsList = orderGoodsMapper.selectList(wrapper);

        for (OrderGoods orderGoods : orderGoodsList) {
            Map<String, Object> goods = new HashMap<>();
            goods.put("id", orderGoods.getId());
            goods.put("goodsId", orderGoods.getGoodsId());
            goods.put("goodsName", orderGoods.getGoodsName());
            goods.put("goodsSn", orderGoods.getGoodsSn());
            goods.put("productId", orderGoods.getProductId());
            goods.put("number", orderGoods.getNumber());
            goods.put("marketPrice", orderGoods.getMarketPrice() != null ? orderGoods.getMarketPrice().setScale(2, BigDecimal.ROUND_HALF_UP).toString() : "0.00");
            goods.put("retailPrice", orderGoods.getRetailPrice() != null ? orderGoods.getRetailPrice().setScale(2, BigDecimal.ROUND_HALF_UP).toString() : "0.00");
            goods.put("listPicUrl", orderGoods.getListPicUrl() != null ? orderGoods.getListPicUrl() : "");
            goods.put("goodsSpecificationIds", orderGoods.getGoodsSpecificationIds() != null ? orderGoods.getGoodsSpecificationIds() : "");
            goods.put("goodsSpecificationNameValue", orderGoods.getGoodsSpecificationNameValue() != null ? orderGoods.getGoodsSpecificationNameValue() : "");

            goodsList.add(goods);
        }

        return goodsList;
    }
    
    /**
     * 根据筛选条件获取用户收益统计
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param status 订单状态
     * @return 收益统计信息
     */
    public Map<String, Object> getEarningsStatsWithFilters(Integer userId, String startDate, String endDate, String status) {
        Map<String, Object> result = new HashMap<>();

        // 获取总收益（已确认）- 带筛选条件
        BigDecimal totalEarnings = getTotalEarningsWithFilters(userId, startDate, endDate, status);
        result.put("totalEarnings", totalEarnings.toString());

        // 获取今日收益 - 带筛选条件
        BigDecimal todayEarnings = getTodayEarningsWithFilters(userId, startDate, endDate, status);
        result.put("todayEarnings", todayEarnings.toString());

        // 获取上月收益 - 带筛选条件
        BigDecimal lastMonthEarnings = getLastMonthEarningsWithFilters(userId, startDate, endDate, status);
        result.put("lastMonthEarnings", lastMonthEarnings.toString());

        // 获取待确认收益 - 带筛选条件
        BigDecimal pendingEarnings = getPendingEarningsWithFilters(userId, startDate, endDate, status);
        result.put("pendingEarnings", pendingEarnings.toString());

        // 获取今日预估收入 - 带筛选条件
        BigDecimal todayEstimated = getTodayEstimatedEarningsWithFilters(userId, startDate, endDate, status);
        result.put("todayEstimated", todayEstimated.toString());

        // 获取本月预估收入 - 带筛选条件
        BigDecimal monthEstimated = getMonthEstimatedEarningsWithFilters(userId, startDate, endDate, status);
        result.put("monthEstimated", monthEstimated.toString());

        // 获取今日订单数 - 带筛选条件
        int todayOrders = getTodayOrderCountWithFilters(userId, startDate, endDate, status);
        result.put("todayOrders", todayOrders);

        // 获取本月订单数 - 带筛选条件
        int monthOrders = getMonthOrderCountWithFilters(userId, startDate, endDate, status);
        result.put("monthOrders", monthOrders);

        // 获取可提现金额 - 带筛选条件
        BigDecimal withdrawableAmount = getWithdrawableAmountWithFilters(userId, startDate, endDate, status);
        result.put("withdrawableAmount", withdrawableAmount.toString());

        // 获取最近收益列表（包含完整订单详情）- 带筛选条件
        List<Map<String, Object>> recentEarnings = getRecentEarningsWithFilters(userId, 10, startDate, endDate, status);
        result.put("earningsList", recentEarnings);

        return result;
    }
    
    /**
     * 带筛选条件的总收益查询
     */
    private BigDecimal getTotalEarningsWithFilters(Integer userId, String startDate, String endDate, String status) {
        return promotionEarningsMapper.selectTotalEarningsWithFilters(userId, startDate, endDate, status);
    }
    
    /**
     * 带筛选条件的今日收益查询
     */
    private BigDecimal getTodayEarningsWithFilters(Integer userId, String startDate, String endDate, String status) {
        LocalDate today = LocalDate.now();
        Date startOfDay = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endOfDay = Date.from(today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        return promotionEarningsMapper.selectEarningsByStatusWithFilters(userId, "confirmed", startOfDay, endOfDay, startDate, endDate, status);
    }
    
    /**
     * 带筛选条件的上月收益查询
     */
    private BigDecimal getLastMonthEarningsWithFilters(Integer userId, String startDateFilter, String endDateFilter, String statusFilter) {
        LocalDate lastMonth = LocalDate.now().minusMonths(1);
        Date startOfMonth = Date.from(lastMonth.withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endOfMonth = Date.from(lastMonth.withDayOfMonth(lastMonth.lengthOfMonth()).plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        return promotionEarningsMapper.selectEarningsByStatusWithFilters(userId, "confirmed", startOfMonth, endOfMonth, startDateFilter, endDateFilter, statusFilter);
    }
    
    /**
     * 带筛选条件的待确认收益查询
     */
    private BigDecimal getPendingEarningsWithFilters(Integer userId, String startDate, String endDate, String status) {
        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.eq("promoter_id", userId)
               .eq("status", "pending");
        
        // 添加日期筛选
        if (startDate != null && !startDate.isEmpty()) {
            wrapper.ge("create_time", startDate);
        }
        if (endDate != null && !endDate.isEmpty()) {
            wrapper.le("create_time", endDate);
        }
        
        // 添加状态筛选
        if (status != null && !status.isEmpty()) {
            wrapper.eq("status", status);
        }

        return this.list(wrapper).stream()
                .map(PromotionEarnings::getCommissionAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    /**
     * 带筛选条件的今日预估收入查询
     */
    private BigDecimal getTodayEstimatedEarningsWithFilters(Integer userId, String startDate, String endDate, String status) {
        LocalDate today = LocalDate.now();
        Date startOfDay = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endOfDay = Date.from(today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        return promotionEarningsMapper.selectEstimatedEarningsWithFilters(userId, startOfDay, endOfDay, startDate, endDate, status);
    }
    
    /**
     * 带筛选条件的本月预估收入查询
     */
    private BigDecimal getMonthEstimatedEarningsWithFilters(Integer userId, String startDate, String endDate, String status) {
        LocalDate thisMonth = LocalDate.now();
        Date startOfMonth = Date.from(thisMonth.withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endOfMonth = Date.from(thisMonth.withDayOfMonth(thisMonth.lengthOfMonth()).plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        return promotionEarningsMapper.selectEstimatedEarningsWithFilters(userId, startOfMonth, endOfMonth, startDate, endDate, status);
    }
    
    /**
     * 带筛选条件的今日订单数查询
     */
    private int getTodayOrderCountWithFilters(Integer userId, String startDate, String endDate, String status) {
        LocalDate today = LocalDate.now();
        Date startOfDay = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endOfDay = Date.from(today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.eq("promoter_id", userId)
               .ge("order_create_time", startOfDay)
               .lt("order_create_time", endOfDay);
        
        // 添加日期筛选
        if (startDate != null && !startDate.isEmpty()) {
            wrapper.ge("order_create_time", startDate);
        }
        if (endDate != null && !endDate.isEmpty()) {
            wrapper.le("order_create_time", endDate);
        }
        
        // 添加状态筛选
        if (status != null && !status.isEmpty()) {
            wrapper.eq("status", status);
        }

        return (int) this.count(wrapper);
    }
    
    /**
     * 带筛选条件的本月订单数查询
     */
    private int getMonthOrderCountWithFilters(Integer userId, String startDate, String endDate, String status) {
        LocalDate thisMonth = LocalDate.now();
        Date startOfMonth = Date.from(thisMonth.withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endOfMonth = Date.from(thisMonth.withDayOfMonth(thisMonth.lengthOfMonth()).plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.eq("promoter_id", userId)
               .ge("order_create_time", startOfMonth)
               .lt("order_create_time", endOfMonth);
        
        // 添加日期筛选
        if (startDate != null && !startDate.isEmpty()) {
            wrapper.ge("order_create_time", startDate);
        }
        if (endDate != null && !endDate.isEmpty()) {
            wrapper.le("order_create_time", endDate);
        }
        
        // 添加状态筛选
        if (status != null && !status.isEmpty()) {
            wrapper.eq("status", status);
        }

        return (int) this.count(wrapper);
    }
    
    /**
     * 带筛选条件的可提现金额查询
     */
    private BigDecimal getWithdrawableAmountWithFilters(Integer userId, String startDate, String endDate, String status) {
        // 获取上月已确认的收益 - 带筛选条件
        BigDecimal lastMonthEarnings = getLastMonthEarningsWithFilters(userId, startDate, endDate, status);
        
        // 获取待确认收益 - 带筛选条件
        BigDecimal pendingEarnings = getPendingEarningsWithFilters(userId, startDate, endDate, status);
        
        return lastMonthEarnings.add(pendingEarnings);
    }
    
    /**
     * 带筛选条件的最近收益列表查询
     */
    private List<Map<String, Object>> getRecentEarningsWithFilters(Integer userId, int limit, String startDate, String endDate, String status) {
        QueryWrapper<PromotionEarnings> wrapper = new QueryWrapper<>();
        wrapper.eq("promoter_id", userId)
               .orderByDesc("create_time")
               .last("LIMIT " + limit);
        
        // 添加日期筛选
        if (startDate != null && !startDate.isEmpty()) {
            wrapper.ge("create_time", startDate);
        }
        if (endDate != null && !endDate.isEmpty()) {
            wrapper.le("create_time", endDate);
        }
        
        // 添加状态筛选
        if (status != null && !status.isEmpty()) {
            wrapper.eq("status", status);
        }

        List<PromotionEarnings> earningsList = this.list(wrapper);
        
        // 格式化数据
        List<Map<String, Object>> formattedList = new ArrayList<>();
        for (PromotionEarnings earnings : earningsList) {
            Map<String, Object> item = new HashMap<>();
            item.put("id", earnings.getId());
            item.put("orderId", earnings.getOrderId());
            item.put("promotedUserId", earnings.getPromotedUserId());
            item.put("orderAmount", earnings.getOrderAmount() != null ? earnings.getOrderAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString() : "0.00");
            item.put("commissionAmount", earnings.getCommissionAmount() != null ? earnings.getCommissionAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString() : "0.00");
            item.put("commissionRate", earnings.getCommissionRate() != null ? earnings.getCommissionRate().toString() : "10");
            item.put("status", earnings.getStatus());
            item.put("createTime", earnings.getCreateTime());
            item.put("confirmTime", earnings.getConfirmTime());
            item.put("orderCreateTime", earnings.getOrderCreateTime());
            
            // 获取订单商品信息
            if (earnings.getOrderId() != null) {
                item.put("orderGoods", getOrderGoodsInfo(earnings.getOrderId()));
            } else {
                item.put("orderGoods", new ArrayList<>());
            }
            
            formattedList.add(item);
        }
        
        return formattedList;
    }
}
