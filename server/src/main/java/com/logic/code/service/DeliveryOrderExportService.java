package com.logic.code.service;

import cn.hutool.core.date.DateUtil;
import com.logic.code.common.Cache;
import com.logic.code.common.utils.ExcelUtils;
import com.logic.code.entity.User;
import com.logic.code.entity.goods.GoodsSpecification;
import com.logic.code.entity.goods.Specification;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.order.OrderGoods;
import com.logic.code.model.vo.OrderDetailVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 配货单导出服务类
 * <AUTHOR>
 * @date 2025/9/5
 */
@Service
@Slf4j
public class DeliveryOrderExportService {

    @Resource
    private Cache cache;

    @Resource
    private OrderService orderService;

    @Resource
    private UserService userService;

    @Resource
    private GoodsSpecificationService goodsSpecificationService;

    @Resource
    private SpecificationService specificationService;

    /**
     * 导出配货单信息到Excel文件
     * @param orderIds 订单ID列表，如果为空则导出所有待发货订单
     * @return 导出的文件路径
     */
    public String exportDeliveryOrdersToExcel(List<Integer> orderIds) {
        try {
            log.info("开始执行配货单导出任务...");
            cache.init();

            // 获取订单列表
            List<Order> orders;
            if (orderIds != null && !orderIds.isEmpty()) {
                // 导出指定订单
                orders = orderService.queryByIds(orderIds);
                log.info("导出指定订单数量: {}", orders.size());
            } else {
                // 导出所有待发货订单
                orders = orderService.queryAll().stream()
                    .filter(order -> order.getPayStatus().getValue() == 2 && // 已支付
                                    order.getOrderStatus().getValue() == 1) // 待发货
                    .collect(Collectors.toList());
                log.info("导出所有待发货订单数量: {}", orders.size());
            }

            if (orders.isEmpty()) {
                log.info("没有找到需要导出的订单数据");
                return null;
            }

            // 创建Excel工作簿
            Workbook workbook = ExcelUtils.createWorkbook();
            Sheet sheet = workbook.createSheet("配货单信息");

            // 创建样式
            CellStyle headerStyle = ExcelUtils.createHeaderStyle(workbook);
            CellStyle dataStyle = ExcelUtils.createDataStyle(workbook);
            CellStyle dateStyle = ExcelUtils.createDateStyle(workbook);

            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "订单编号", "收件人", "电话",
                "地址", "商品", "规格",
                "数量",
                "下单时间", "备注"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                ExcelUtils.setCellValue(cell, headers[i], headerStyle);
            }

            // 填充数据
            int rowIndex = 1;
            for (Order order : orders) {
                try {
                    // 获取用户信息
                    User user = userService.queryById(order.getUserId());

                    // 获取订单详情
                    OrderDetailVO orderDetailVO = orderService.queryOrderDetail(order.getId());
                    List<OrderGoods> orderGoods = orderDetailVO.getOrderGoods();

                    // 构建完整地址
                    String fullAddress = "";
                    if (order.getProvince() != null && order.getCity() != null && order.getDistrict() != null) {
                        String provinceName = cache.region.get(order.getProvince());
                        String cityName = cache.region.get(order.getCity());
                        String districtName = cache.region.get(order.getDistrict());
                        fullAddress = ExcelUtils.safeString(provinceName) +
                                     ExcelUtils.safeString(cityName) +
                                     ExcelUtils.safeString(districtName) +
                                     ExcelUtils.safeString(order.getAddress());
                    }

                    // 处理每个商品
                    for (OrderGoods goods : orderGoods) {
                        Row dataRow = sheet.createRow(rowIndex++);
                        int cellIndex = 0;

                        // 填充数据
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getOrderSn(), dataStyle);
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getConsignee(), dataStyle);
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getMobile(), dataStyle);
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), fullAddress, dataStyle);
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), goods.getGoodsName(), dataStyle);
                        
                        // 规格信息
                        String specifications = getProductSpecifications(goods.getGoodsSpecificationIds());
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), specifications, dataStyle);
                        
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), goods.getNumber(), dataStyle);
                        //ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), goods.getRetailPrice(), dataStyle);
                        
                        // 计算总价
                       /* BigDecimal totalPrice = goods.getRetailPrice().multiply(new BigDecimal(goods.getNumber()));
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), totalPrice.doubleValue(), dataStyle);*/
                        
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getCreateTime(), dateStyle);
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getPostscript(), dataStyle);
                    }

                } catch (Exception e) {
                    log.error("处理订单 {} 时出错: {}", order.getId(), e.getMessage(), e);
                }
            }

            // 自动调整列宽
            ExcelUtils.autoSizeColumns(sheet, headers.length);

            // 保存文件
            String date = DateUtil.format(new Date(), "yyyyMMddHHmmss");
            String fileName = "配货单导出_" + date + ".xlsx";
            String filePath = System.getProperty("user.dir") + "/" + fileName;
            ExcelUtils.saveWorkbook(workbook, filePath);

            // 关闭工作簿
            workbook.close();

            log.info("配货单导出完成！");
            log.info("导出文件路径: {}", filePath);
            log.info("导出订单数量: {}", orders.size());

            return filePath;

        } catch (IOException e) {
            log.error("导出Excel文件时出错: {}", e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("导出过程中出现异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 导出配货单信息到Excel字节数组（用于浏览器下载）
     * @param orderIds 订单ID列表，如果为空则导出所有待发货订单
     * @return Excel文件的字节数组
     */
    public byte[] exportDeliveryOrdersToExcelBytes(List<Integer> orderIds) {
        try {
            log.info("开始执行配货单导出任务（字节数组）...");
            cache.init();

            // 获取订单列表
            List<Order> orders;
            if (orderIds != null && !orderIds.isEmpty()) {
                // 导出指定订单
                orders = orderService.queryByIds(orderIds);
                log.info("导出指定订单数量: {}", orders.size());
            } else {
                // 导出所有待发货订单
                orders = orderService.queryAll().stream()
                    .filter(order -> order.getPayStatus().getValue() == 2 && // 已支付
                                    order.getOrderStatus().getValue() == 1) // 待发货
                    .collect(Collectors.toList());
                log.info("导出所有待发货订单数量: {}", orders.size());
            }

            if (orders.isEmpty()) {
                log.info("没有找到需要导出的订单数据");
                return null;
            }

            // 创建Excel工作簿
            Workbook workbook = ExcelUtils.createWorkbook();
            Sheet sheet = workbook.createSheet("配货单信息");

            // 创建样式
            CellStyle headerStyle = ExcelUtils.createHeaderStyle(workbook);
            CellStyle dataStyle = ExcelUtils.createDataStyle(workbook);
            CellStyle dateStyle = ExcelUtils.createDateStyle(workbook);

            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "订单编号", "收件人", "电话",
                "地址", "商品", "规格",
                "数量",
                "下单时间", "备注"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                ExcelUtils.setCellValue(cell, headers[i], headerStyle);
            }

            // 填充数据
            int rowIndex = 1;
            for (Order order : orders) {
                try {
                    // 获取用户信息
                    User user = userService.queryById(order.getUserId());

                    // 获取订单详情
                    OrderDetailVO orderDetailVO = orderService.queryOrderDetail(order.getId());
                    List<OrderGoods> orderGoods = orderDetailVO.getOrderGoods();

                    // 构建完整地址
                    String fullAddress = "";
                    if (order.getProvince() != null && order.getCity() != null && order.getDistrict() != null) {
                        String provinceName = cache.region.get(order.getProvince());
                        String cityName = cache.region.get(order.getCity());
                        String districtName = cache.region.get(order.getDistrict());
                        fullAddress = ExcelUtils.safeString(provinceName) +
                                     ExcelUtils.safeString(cityName) +
                                     ExcelUtils.safeString(districtName) +
                                     ExcelUtils.safeString(order.getAddress());
                    }

                    // 处理每个商品
                    for (OrderGoods goods : orderGoods) {
                        Row dataRow = sheet.createRow(rowIndex++);
                        int cellIndex = 0;

                        // 填充数据
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getOrderSn(), dataStyle);
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getConsignee(), dataStyle);
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getMobile(), dataStyle);
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), fullAddress, dataStyle);
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), goods.getGoodsName(), dataStyle);
                        
                        // 规格信息
                        String specifications = getProductSpecifications(goods.getGoodsSpecificationIds());
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), specifications, dataStyle);
                        
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), goods.getNumber(), dataStyle);
                       // ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), goods.getRetailPrice(), dataStyle);
                        
                        // 计算总价
                    /*    BigDecimal totalPrice = goods.getRetailPrice().multiply(new BigDecimal(goods.getNumber()));
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), totalPrice.doubleValue(), dataStyle);*/
                        
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getCreateTime(), dateStyle);
                        ExcelUtils.setCellValue(dataRow.createCell(cellIndex++), order.getPostscript(), dataStyle);
                    }

                } catch (Exception e) {
                    log.error("处理订单 {} 时出错: {}", order.getId(), e.getMessage(), e);
                }
            }

            // 自动调整列宽
            ExcelUtils.autoSizeColumns(sheet, headers.length);

            // 转换为字节数组
            byte[] excelBytes = ExcelUtils.workbookToBytes(workbook);

            // 关闭工作簿
            workbook.close();

            log.info("配货单导出完成（字节数组）！");
            log.info("导出订单数量: {}", orders.size());

            return excelBytes;

        } catch (IOException e) {
            log.error("导出Excel文件时出错: {}", e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("导出过程中出现异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据商品规格ID字符串获取规格信息
     * @param goodsSpecificationIds 规格ID字符串，格式如 "1,2,3"
     * @return 规格信息字符串，格式如 "颜色:红色,尺寸:L"
     */
    private String getProductSpecifications(String goodsSpecificationIds) {
        if (goodsSpecificationIds == null || goodsSpecificationIds.trim().isEmpty()) {
            return "";
        }

        try {
            String[] specIds = goodsSpecificationIds.split(",");
            List<String> specTexts = new ArrayList<>();

            for (String specIdStr : specIds) {
                try {
                    Integer specId = Integer.parseInt(specIdStr.trim());
                    GoodsSpecification goodsSpec = goodsSpecificationService.queryById(specId);
                    if (goodsSpec != null) {
                        Specification spec = specificationService.findById(goodsSpec.getSpecificationId());
                        if (spec != null) {
                            specTexts.add(spec.getName() + ":" + goodsSpec.getValue());
                        }
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的规格ID: {}", specIdStr);
                }
            }

            return String.join(",", specTexts);
        } catch (Exception e) {
            log.error("获取商品规格信息时出错: {}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * 获取配货单预览数据（用于弹窗显示）
     * @param orderId 订单ID
     * @return 配货单预览数据
     */
    public Map<String, Object> getDeliveryOrderPreview(Integer orderId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Order order = orderService.queryById(orderId);
            if (order == null) {
                throw new RuntimeException("订单不存在");
            }

            // 获取订单详情
            OrderDetailVO orderDetailVO = orderService.queryOrderDetail(orderId);
            List<OrderGoods> orderGoods = orderDetailVO.getOrderGoods();

            // 构建完整地址
            String fullAddress = "";
            if (order.getProvince() != null && order.getCity() != null && order.getDistrict() != null) {
                String provinceName = cache.region.get(order.getProvince());
                String cityName = cache.region.get(order.getCity());
                String districtName = cache.region.get(order.getDistrict());
                fullAddress = provinceName + cityName + districtName + order.getAddress();
            }

            // 处理商品信息
            List<Map<String, Object>> goodsList = new ArrayList<>();
            for (OrderGoods goods : orderGoods) {
                Map<String, Object> goodsInfo = new HashMap<>();
                goodsInfo.put("goodsName", goods.getGoodsName());
                goodsInfo.put("specifications", getProductSpecifications(goods.getGoodsSpecificationIds()));
                goodsInfo.put("number", goods.getNumber());
                goodsInfo.put("price", goods.getRetailPrice());
                goodsInfo.put("totalPrice", goods.getRetailPrice().multiply(new BigDecimal(goods.getNumber())));
                goodsList.add(goodsInfo);
            }

            result.put("orderSn", order.getOrderSn());
            result.put("consignee", order.getConsignee());
            result.put("mobile", order.getMobile());
            result.put("address", fullAddress);
            result.put("createTime", order.getCreateTime());
            result.put("postscript", order.getPostscript());
            result.put("goodsList", goodsList);
            result.put("totalAmount", order.getActualPrice());

        } catch (Exception e) {
            log.error("获取配货单预览数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取配货单数据失败: " + e.getMessage());
        }

        return result;
    }
}