package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.entity.goods.GoodsViewLog;
import com.logic.code.mapper.GoodsViewLogMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 商品浏览记录服务类
 * <AUTHOR>
 */
@Service
@Slf4j
public class GoodsViewLogService {

    @Resource
    private GoodsViewLogMapper goodsViewLogMapper;

    /**
     * 记录商品浏览日志
     * @param goodsId 商品ID
     * @param userId 用户ID
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @param sessionId 会话ID
     * @param referer 来源页面
     */
    public void recordGoodsViewLog(Integer goodsId, Integer userId, String ipAddress,
                                 String userAgent, String sessionId, String referer) {
        try {
            if (userId == null) {
                log.warn("用户ID为空，无法记录商品浏览日志，商品ID: {}", goodsId);
                return;
            }

            // 检查是否为首次浏览
            boolean isFirstView = isFirstView(userId, goodsId);

            // 创建浏览记录
            GoodsViewLog viewLog = new GoodsViewLog();
            viewLog.setUserId(userId);
            viewLog.setGoodsId(goodsId);
            viewLog.setViewTime(new Date());
            viewLog.setIpAddress(ipAddress);
            viewLog.setUserAgent(userAgent);
            viewLog.setSourceType(1); // 1-小程序
            viewLog.setSessionId(sessionId);
            viewLog.setReferer(referer);
            viewLog.setViewDuration(0); // 初始为0，后续可以通过埋点更新
            viewLog.setIsFirstView(isFirstView ? 1 : 0);
            viewLog.setCreatedAt(new Date());
            viewLog.setUpdatedAt(new Date());

            // 保存记录
            goodsViewLogMapper.insert(viewLog);
            log.info("记录商品浏览日志成功，用户ID: {}, 商品ID: {}, 是否首次: {}",
                    userId, goodsId, isFirstView);

        } catch (Exception e) {
            log.error("记录商品浏览日志失败，商品ID: {}", goodsId, e);
        }
    }

    /**
     * 记录商品浏览日志（简化版本）
     * @param goodsId 商品ID
     * @param userId 用户ID
     */
    public void recordGoodsViewLog(Integer goodsId, Integer userId) {
        recordGoodsViewLog(goodsId, userId, null, null, null, null);
    }

    /**
     * 检查用户是否首次浏览该商品
     * @param userId 用户ID
     * @param goodsId 商品ID
     * @return true-首次浏览，false-非首次浏览
     */
    private boolean isFirstView(Integer userId, Integer goodsId) {
        try {
            QueryWrapper<GoodsViewLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .eq("goods_id", goodsId);
            long count = goodsViewLogMapper.selectCount(queryWrapper);
            return count == 0;
        } catch (Exception e) {
            log.error("检查首次浏览状态失败，用户ID: {}, 商品ID: {}", userId, goodsId, e);
            return true; // 出错时默认认为是首次浏览
        }
    }

    /**
     * 获取指定时间范围内的商品浏览量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 浏览量
     */
    public Long getViewCountBetween(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return goodsViewLogMapper.getViewCountBetween(startTime, endTime);
        } catch (Exception e) {
            log.error("获取时间范围内的商品浏览量失败，开始时间: {}, 结束时间: {}", startTime, endTime, e);
            return 0L;
        }
    }

    /**
     * 获取今日商品浏览量
     * @return 今日商品浏览量
     */
    public Long getTodayViewCount() {
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        LocalDateTime todayEnd = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
        return getViewCountBetween(todayStart, todayEnd);
    }

    /**
     * 获取昨日商品浏览量
     * @return 昨日商品浏览量
     */
    public Long getYesterdayViewCount() {
        LocalDateTime yesterdayStart = LocalDateTime.now().minusDays(1).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime yesterdayEnd = LocalDateTime.now().minusDays(1).withHour(23).withMinute(59).withSecond(59);
        return getViewCountBetween(yesterdayStart, yesterdayEnd);
    }
}