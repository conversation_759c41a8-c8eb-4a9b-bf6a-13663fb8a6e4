-- 测试商品浏览日志记录功能
-- 1. 查看商品浏览记录表结构
DESCRIBE goods_view_log;

-- 2. 插入测试数据
INSERT INTO goods_view_log (user_id, goods_id, view_time, ip_address, user_agent, source_type, session_id, referer, view_duration, is_first_view) 
VALUES 
(1, 1, NOW(), '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)', 1, 'session123', '/pages/goods/list', 30, 1),
(1, 2, NOW(), '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)', 1, 'session123', '/pages/goods/list', 45, 1),
(2, 1, NOW(), '*************', 'Mozilla/5.0 (Android 11; Mobile)', 1, 'session456', '/pages/index/index', 20, 1);

-- 3. 查询用户浏览记录
SELECT * FROM goods_view_log WHERE user_id = 1 ORDER BY view_time DESC;

-- 4. 查询商品浏览统计
SELECT 
    goods_id,
    COUNT(*) as total_views,
    COUNT(DISTINCT user_id) as unique_users,
    SUM(CASE WHEN is_first_view = 1 THEN 1 ELSE 0 END) as first_time_views
FROM goods_view_log 
GROUP BY goods_id 
ORDER BY total_views DESC;

-- 5. 查询用户首次浏览记录
SELECT * FROM goods_view_log WHERE user_id = 1 AND goods_id = 1 AND is_first_view = 1;

-- 6. 查询今日浏览记录
SELECT * FROM goods_view_log WHERE DATE(view_time) = CURDATE() ORDER BY view_time DESC;

-- 7. 查询热门商品（按浏览次数排序）
SELECT 
    g.id,
    g.name,
    COUNT(vl.id) as view_count
FROM goods g
LEFT JOIN goods_view_log vl ON g.id = vl.goods_id
GROUP BY g.id, g.name
ORDER BY view_count DESC
LIMIT 10;