-- 商品浏览记录表
CREATE TABLE IF NOT EXISTS `goods_view_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `goods_id` int(11) NOT NULL COMMENT '商品ID',
    `view_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '浏览时间',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
    `source_type` tinyint(1) DEFAULT 1 COMMENT '来源类型：1-小程序，2-H5，3-APP',
    `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID',
    `referer` varchar(500) DEFAULT NULL COMMENT '来源页面',
    `view_duration` int(11) DEFAULT 0 COMMENT '浏览时长（秒）',
    `is_first_view` tinyint(1) DEFAULT 1 COMMENT '是否首次浏览：0-否，1-是',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_goods_id` (`goods_id`),
    KEY `idx_view_time` (`view_time`),
    KEY `idx_user_goods` (`user_id`, `goods_id`),
    KEY `idx_session_id` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品浏览记录表';

-- 添加表注释
ALTER TABLE `goods_view_log` COMMENT = '商品浏览记录表，用于记录用户浏览商品的详细日志';

-- 创建联合索引优化查询性能
CREATE INDEX idx_user_view_time ON goods_view_log(user_id, view_time DESC);
CREATE INDEX idx_goods_view_time ON goods_view_log(goods_id, view_time DESC);